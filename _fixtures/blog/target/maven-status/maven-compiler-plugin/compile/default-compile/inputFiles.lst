/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/main/java/com/shpota/blog/controller/BlogController.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/main/java/com/shpota/blog/model/BlogRepository.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/main/java/com/shpota/blog/model/Post.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/main/java/com/shpota/blog/model/RequestHandler.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/main/java/com/shpota/blog/model/exception/RepositoryException.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/main/java/com/shpota/blog/model/jdbc/JdbcBlogRepository.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/main/java/com/shpota/blog/model/strategies/AddPostStrategy.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/main/java/com/shpota/blog/model/strategies/AllPostsStrategy.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/main/java/com/shpota/blog/model/strategies/CreatePostStrategy.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/main/java/com/shpota/blog/model/strategies/DeletePostStrategy.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/main/java/com/shpota/blog/model/strategies/EditPostStrategy.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/main/java/com/shpota/blog/model/strategies/ErrorStrategy.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/main/java/com/shpota/blog/model/strategies/PostStrategy.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/main/java/com/shpota/blog/model/strategies/RedirectAllPostsStrategy.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/main/java/com/shpota/blog/model/strategies/RedirectCreatePostStrategy.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/main/java/com/shpota/blog/model/strategies/SavePostStrategy.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/main/java/com/shpota/blog/model/strategies/Strategy.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/main/java/com/shpota/blog/util/Assert.java
