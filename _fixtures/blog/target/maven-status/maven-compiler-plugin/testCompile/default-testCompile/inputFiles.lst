/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/test/java/com/shpota/blog/model/PostTest.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/test/java/com/shpota/blog/model/RequestHandlerTest.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/test/java/com/shpota/blog/model/strategies/AddPostStrategyTest.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/test/java/com/shpota/blog/model/strategies/AllPostsStrategyTest.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/test/java/com/shpota/blog/model/strategies/CreatePostStrategyTest.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/test/java/com/shpota/blog/model/strategies/DeletePostStrategyTest.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/test/java/com/shpota/blog/model/strategies/EditPostStrategyTest.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/test/java/com/shpota/blog/model/strategies/ErrorStrategyTest.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/test/java/com/shpota/blog/model/strategies/PostStrategyTest.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/test/java/com/shpota/blog/model/strategies/RedirectAllPostsStrategyTest.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/test/java/com/shpota/blog/model/strategies/RedirectCreatePostStrategyTest.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/test/java/com/shpota/blog/model/strategies/SavePostStrategyTest.java
/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/src/test/java/com/shpota/blog/util/AssertTest.java
