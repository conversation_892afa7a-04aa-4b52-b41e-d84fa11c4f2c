<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.shpota.blog.model.strategies.ErrorStrategyTest" time="0.0" tests="1" errors="1" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="23"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/target/test-classes:/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.13.1/junit-4.13.1.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-all/1.9.5/mockito-all-1.9.5.jar:/Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/3.0.1/javax.servlet-api-3.0.1.jar:/Users/<USER>/.m2/repository/log4j/log4j/1.2.17/log4j-1.2.17.jar:/Users/<USER>/.m2/repository/javax/servlet/jstl/1.2/jstl-1.2.jar:/Users/<USER>/.m2/repository/pl/pragmatists/JUnitParams/1.0.5/JUnitParams-1.0.5.jar:"/>
    <property name="java.vm.vendor" value="Homebrew"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="23"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="/usr/local/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib"/>
    <property name="sun.java.command" value="/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/target/surefire/surefirebooter-20250709204524916_3.jar /Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/target/surefire 2025-07-09T20-45-24_844-jvmRun1 surefire-20250709204524916_1tmp surefire_0-20250709204524916_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/target/test-classes:/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.13.1/junit-4.13.1.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-all/1.9.5/mockito-all-1.9.5.jar:/Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/3.0.1/javax.servlet-api-3.0.1.jar:/Users/<USER>/.m2/repository/log4j/log4j/1.2.17/log4j-1.2.17.jar:/Users/<USER>/.m2/repository/javax/servlet/jstl/1.2/jstl-1.2.jar:/Users/<USER>/.m2/repository/pl/pragmatists/JUnitParams/1.0.5/JUnitParams-1.0.5.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="en"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-01-21"/>
    <property name="java.home" value="/usr/local/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="apple.awt.application.name" value="ForkedBooter"/>
    <property name="surefire.real.class.path" value="/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog/target/surefire/surefirebooter-20250709204524916_3.jar"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="23.0.2"/>
    <property name="user.name" value="phodal"/>
    <property name="stdout.encoding" value="UTF-8"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.5"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="java.vendor.version" value="Homebrew"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues"/>
    <property name="java.io.tmpdir" value="/var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/"/>
    <property name="java.version" value="23.0.2"/>
    <property name="user.dir" value="/Users/<USER>/works/ai4m/autodev-legacy/test/_fixtures/blog"/>
    <property name="os.arch" value="x86_64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="UTF-8"/>
    <property name="java.vendor" value="Homebrew"/>
    <property name="java.vm.version" value="23.0.2"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="java.class.version" value="67.0"/>
  </properties>
  <testcase name="shouldHandle" classname="com.shpota.blog.model.strategies.ErrorStrategyTest" time="0.0">
    <error message="Could not initialize class org.mockito.internal.creation.jmock.ClassImposterizer$3" type="java.lang.NoClassDefFoundError"><![CDATA[java.lang.NoClassDefFoundError: Could not initialize class org.mockito.internal.creation.jmock.ClassImposterizer$3
	at org.mockito.internal.creation.jmock.ClassImposterizer.createProxyClass(ClassImposterizer.java:85)
	at org.mockito.internal.creation.jmock.ClassImposterizer.imposterise(ClassImposterizer.java:62)
	at org.mockito.internal.creation.jmock.ClassImposterizer.imposterise(ClassImposterizer.java:56)
	at org.mockito.internal.creation.CglibMockMaker.createMock(CglibMockMaker.java:23)
	at org.mockito.internal.util.MockUtil.createMock(MockUtil.java:26)
	at org.mockito.internal.MockitoCore.mock(MockitoCore.java:51)
	at org.mockito.Mockito.mock(Mockito.java:1243)
	at org.mockito.Mockito.mock(Mockito.java:1120)
	at com.shpota.blog.model.strategies.ErrorStrategyTest.shouldHandle(ErrorStrategyTest.java:17)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:316)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeWithRerun(JUnit4Provider.java:240)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:214)
	at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:155)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
Caused by: java.lang.ExceptionInInitializerError: Exception java.lang.ExceptionInInitializerError [in thread "main"]
	at org.mockito.cglib.core.KeyFactory$Generator.generateClass(KeyFactory.java:167)
	at org.mockito.cglib.core.DefaultGeneratorStrategy.generate(DefaultGeneratorStrategy.java:25)
	at org.mockito.cglib.core.AbstractClassGenerator.create(AbstractClassGenerator.java:217)
	at org.mockito.cglib.core.KeyFactory$Generator.create(KeyFactory.java:145)
	at org.mockito.cglib.core.KeyFactory.create(KeyFactory.java:117)
	at org.mockito.cglib.core.KeyFactory.create(KeyFactory.java:109)
	at org.mockito.cglib.core.KeyFactory.create(KeyFactory.java:105)
	at org.mockito.cglib.proxy.Enhancer.<clinit>(Enhancer.java:70)
	at org.mockito.internal.creation.jmock.ClassImposterizer.createProxyClass(ClassImposterizer.java:85)
	at org.mockito.internal.creation.jmock.ClassImposterizer.imposterise(ClassImposterizer.java:62)
	at org.mockito.internal.creation.jmock.ClassImposterizer.imposterise(ClassImposterizer.java:56)
	at org.mockito.internal.creation.CglibMockMaker.createMock(CglibMockMaker.java:23)
	at org.mockito.internal.util.MockUtil.createMock(MockUtil.java:26)
	at org.mockito.internal.MockitoCore.mock(MockitoCore.java:51)
	at org.mockito.Mockito.mock(Mockito.java:1243)
	at org.mockito.Mockito.mock(Mockito.java:1120)
	at com.shpota.blog.model.strategies.CreatePostStrategyTest.shouldHandle(CreatePostStrategyTest.java:17)
	... 26 more
]]></error>
  </testcase>
</testsuite>