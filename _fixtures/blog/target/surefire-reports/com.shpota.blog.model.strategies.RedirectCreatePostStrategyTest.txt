-------------------------------------------------------------------------------
Test set: com.shpota.blog.model.strategies.RedirectCreatePostStrategyTest
-------------------------------------------------------------------------------
Tests run: 1, Failures: 0, Errors: 1, Skipped: 0, Time elapsed: 0.001 s <<< FAILURE! -- in com.shpota.blog.model.strategies.RedirectCreatePostStrategyTest
com.shpota.blog.model.strategies.RedirectCreatePostStrategyTest.shouldHandle -- Time elapsed: 0 s <<< ERROR!
java.lang.NoClassDefFoundError: Could not initialize class org.mockito.internal.creation.jmock.ClassImposterizer$3
	at org.mockito.internal.creation.jmock.ClassImposterizer.createProxyClass(ClassImposterizer.java:85)
	at org.mockito.internal.creation.jmock.ClassImposterizer.imposterise(ClassImposterizer.java:62)
	at org.mockito.internal.creation.jmock.ClassImposterizer.imposterise(ClassImposterizer.java:56)
	at org.mockito.internal.creation.CglibMockMaker.createMock(CglibMockMaker.java:23)
	at org.mockito.internal.util.MockUtil.createMock(MockUtil.java:26)
	at org.mockito.internal.MockitoCore.mock(MockitoCore.java:51)
	at org.mockito.Mockito.mock(Mockito.java:1243)
	at org.mockito.Mockito.mock(Mockito.java:1120)
	at com.shpota.blog.model.strategies.RedirectCreatePostStrategyTest.shouldHandle(RedirectCreatePostStrategyTest.java:15)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:316)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeWithRerun(JUnit4Provider.java:240)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:214)
	at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:155)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
Caused by: java.lang.ExceptionInInitializerError: Exception java.lang.ExceptionInInitializerError [in thread "main"]
	at org.mockito.cglib.core.KeyFactory$Generator.generateClass(KeyFactory.java:167)
	at org.mockito.cglib.core.DefaultGeneratorStrategy.generate(DefaultGeneratorStrategy.java:25)
	at org.mockito.cglib.core.AbstractClassGenerator.create(AbstractClassGenerator.java:217)
	at org.mockito.cglib.core.KeyFactory$Generator.create(KeyFactory.java:145)
	at org.mockito.cglib.core.KeyFactory.create(KeyFactory.java:117)
	at org.mockito.cglib.core.KeyFactory.create(KeyFactory.java:109)
	at org.mockito.cglib.core.KeyFactory.create(KeyFactory.java:105)
	at org.mockito.cglib.proxy.Enhancer.<clinit>(Enhancer.java:70)
	at org.mockito.internal.creation.jmock.ClassImposterizer.createProxyClass(ClassImposterizer.java:85)
	at org.mockito.internal.creation.jmock.ClassImposterizer.imposterise(ClassImposterizer.java:62)
	at org.mockito.internal.creation.jmock.ClassImposterizer.imposterise(ClassImposterizer.java:56)
	at org.mockito.internal.creation.CglibMockMaker.createMock(CglibMockMaker.java:23)
	at org.mockito.internal.util.MockUtil.createMock(MockUtil.java:26)
	at org.mockito.internal.MockitoCore.mock(MockitoCore.java:51)
	at org.mockito.Mockito.mock(Mockito.java:1243)
	at org.mockito.Mockito.mock(Mockito.java:1120)
	at com.shpota.blog.model.strategies.CreatePostStrategyTest.shouldHandle(CreatePostStrategyTest.java:17)
	... 26 more

