<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - MappingExampleTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>MappingExampleTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.phodal.legacy.examples.html">com.phodal.legacy.examples</a> &gt; MappingExampleTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">1</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.023s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
<li>
<a href="#">Standard output</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">testMappingExampleFlow()</td>
<td class="success">0.023s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>=== JSP to Bytecode Mapping Example ===

Creating example JSP components...
✓ Created 3 JSP components

Creating example bytecode components...
✓ Created 4 bytecode components (3 servlets, 1 regular class)

23:06:04.764 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: createMappings in component: JspBytecodeMapper
23:06:04.765 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Creating mappings for 3 JSP components and 4 bytecode components
23:06:04.767 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Successfully created 3 mappings out of 3 JSP components
23:06:04.768 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: createMappings in component: JspBytecodeMapper
=== Mapping Results ===
Total mappings created: 3
Total bytecode classes mapped: 3
Total dependencies mapped: 5
Total class mappings: 3
Total method mappings: 3
Total dependency mappings: 7
Total tag library mappings: 3

Individual mappings:
  /webapp/login.jsp -&gt; org.apache.jsp.login_jsp
    Confidence: 100.00%
    Dependencies: 2
    Tag Libraries: [http://java.sun.com/jsp/jstl/core]

  /webapp/index.jsp -&gt; org.apache.jsp.index_jsp
    Confidence: 100.00%
    Dependencies: 2
    Tag Libraries: [http://java.sun.com/jsp/jstl/core, http://java.sun.com/jsp/jstl/fmt]

  /webapp/admin/dashboard.jsp -&gt; org.apache.jsp.admin.dashboard_jsp
    Confidence: 100.00%
    Dependencies: 3

=== Mapping Queries ===
Found mapping for /webapp/index.jsp:
  Servlet class: org.apache.jsp.index_jsp
  Dependencies: 2

Mappings using org.apache.jsp.index_jsp: 1
Mappings using java.util.List dependency: 2

All mapped bytecode classes:
  org.apache.jsp.login_jsp
  org.apache.jsp.admin.dashboard_jsp
  org.apache.jsp.index_jsp

All mapped dependencies:
  java.util.List
  com.example.AuthService
  com.example.UserService
  java.util.Map
  com.example.AdminService

=== Example Complete ===
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at Jul 9, 2025, 11:06:05 PM</p>
</div>
</div>
</body>
</html>
