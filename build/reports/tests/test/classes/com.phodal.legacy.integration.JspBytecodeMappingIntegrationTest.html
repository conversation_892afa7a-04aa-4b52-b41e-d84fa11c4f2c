<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - JspBytecodeMappingIntegrationTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>JspBytecodeMappingIntegrationTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.phodal.legacy.integration.html">com.phodal.legacy.integration</a> &gt; JspBytecodeMappingIntegrationTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">4</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.023s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
<li>
<a href="#">Standard output</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">testEndToEndMappingFlow()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testJspAnalysisWithMappingHints()</td>
<td class="success">0.013s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testMappingWithComplexDependencies()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testMappingWithTagLibraries()</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>23:18:56.397 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: createMappings in component: JspBytecodeMapper
23:18:56.397 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Creating mappings for 1 JSP components and 1 bytecode components
23:18:56.397 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Successfully created 1 mappings out of 1 JSP components
23:18:56.398 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: createMappings in component: JspBytecodeMapper
23:18:56.400 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1776655560448417274/test.jsp
23:18:56.401 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1776655560448417274/test.jsp
23:18:56.401 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1776655560448417274/test.jsp
23:18:56.407 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1776655560448417274/test.jsp
23:18:56.407 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1776655560448417274/test.jsp
23:18:56.408 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1776655560448417274/test.jsp
23:18:56.414 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: createMappings in component: JspBytecodeMapper
23:18:56.414 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Creating mappings for 2 JSP components and 3 bytecode components
23:18:56.414 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Successfully created 2 mappings out of 2 JSP components
23:18:56.414 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: createMappings in component: JspBytecodeMapper
23:18:56.417 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: createMappings in component: JspBytecodeMapper
23:18:56.418 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Creating mappings for 1 JSP components and 1 bytecode components
23:18:56.418 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Successfully created 1 mappings out of 1 JSP components
23:18:56.419 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: createMappings in component: JspBytecodeMapper
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at Jul 9, 2025, 11:18:57 PM</p>
</div>
</div>
</body>
</html>
