<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - JavaCodeAnalyzerTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>JavaCodeAnalyzerTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.phodal.legacy.parser.html">com.phodal.legacy.parser</a> &gt; JavaCodeAnalyzerTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">9</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.189s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
<li>
<a href="#">Standard output</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">testAnalyzeEmptyDirectory()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeFilterClass()</td>
<td class="success">0.043s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeInterfaceAndEnum()</td>
<td class="success">0.011s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeInvalidJavaFile()</td>
<td class="success">0.087s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeListenerClass()</td>
<td class="success">0.007s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeMultipleJavaFiles()</td>
<td class="success">0.016s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeNonJavaFiles()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeServletClass()</td>
<td class="success">0.010s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeSimpleJavaClass()</td>
<td class="success">0.009s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>23:18:56.494 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit5841971925484054154/InvalidClass.java
23:18:56.494 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit5841971925484054154/InvalidClass.java
23:18:56.495 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit5841971925484054154/InvalidClass.java
23:18:56.575 [Test worker] WARN com.phodal.legacy.parser.JavaCodeAnalyzer -- Failed to parse Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit5841971925484054154/InvalidClass.java - [(line 3,col 27) Parse error. Found &lt;EOF&gt;, expected one of  &quot;;&quot; &quot;&lt;&quot; &quot;@&quot; &quot;abstract&quot; &quot;boolean&quot; &quot;byte&quot; &quot;char&quot; &quot;class&quot; &quot;default&quot; &quot;double&quot; &quot;enum&quot; &quot;exports&quot; &quot;final&quot; &quot;float&quot; &quot;int&quot; &quot;interface&quot; &quot;long&quot; &quot;module&quot; &quot;native&quot; &quot;non-sealed&quot; &quot;open&quot; &quot;opens&quot; &quot;permits&quot; &quot;private&quot; &quot;protected&quot; &quot;provides&quot; &quot;public&quot; &quot;record&quot; &quot;requires&quot; &quot;sealed&quot; &quot;short&quot; &quot;static&quot; &quot;strictfp&quot; &quot;synchronized&quot; &quot;to&quot; &quot;transient&quot; &quot;transitive&quot; &quot;uses&quot; &quot;void&quot; &quot;volatile&quot; &quot;with&quot; &quot;yield&quot; &quot;{&quot; &quot;}&quot; &lt;IDENTIFIER&gt;
Problem stacktrace : 
  com.github.javaparser.GeneratedJavaParser.generateParseException(GeneratedJavaParser.java:14041)
  com.github.javaparser.GeneratedJavaParser.jj_consume_token(GeneratedJavaParser.java:13886)
  com.github.javaparser.GeneratedJavaParser.ClassOrInterfaceBody(GeneratedJavaParser.java:1295)
  com.github.javaparser.GeneratedJavaParser.ClassOrInterfaceDeclaration(GeneratedJavaParser.java:538)
  com.github.javaparser.GeneratedJavaParser.CompilationUnit(GeneratedJavaParser.java:156)
  com.github.javaparser.JavaParser.parse(JavaParser.java:125)
  com.github.javaparser.JavaParser.parse(JavaParser.java:305)
  com.phodal.legacy.parser.JavaCodeAnalyzer.analyzeJavaFile(JavaCodeAnalyzer.java:84)
  com.phodal.legacy.parser.JavaCodeAnalyzerTest.testAnalyzeInvalidJavaFile(JavaCodeAnalyzerTest.java:329)
  java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
  java.base/java.lang.reflect.Method.invoke(Method.java:580)
  org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)
  org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
  org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
  org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
  org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
  org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
  org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
  org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
  org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
  org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
  org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
  org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
  org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
  org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
  org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:218)
  org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
  org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:214)
  org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:139)
  org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
  org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
  org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
  org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
  java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
  org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
  org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
  org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
  org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
  java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
  org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
  org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
  org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
  org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
  org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
  org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
  org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
  org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:107)
  org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:88)
  org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:54)
  org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:67)
  org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:52)
  org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:114)
  org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:86)
  org.junit.platform.launcher.core.DefaultLauncherSession$DelegatingLauncher.execute(DefaultLauncherSession.java:86)
  org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
  org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
  org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
  org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
  java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
  java.base/java.lang.reflect.Method.invoke(Method.java:580)
  org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
  org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
  org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
  org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
  jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
  org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
  org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
  org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
  org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
  org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
  org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:122)
  org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:72)
  worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
  worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)]
23:18:56.582 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7647482750527387352/LoggingFilter.java
23:18:56.582 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7647482750527387352/LoggingFilter.java
23:18:56.582 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7647482750527387352/LoggingFilter.java
23:18:56.622 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7647482750527387352/LoggingFilter.java
23:18:56.627 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7761869031979312067/TestServlet.java
23:18:56.627 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7761869031979312067/TestServlet.java
23:18:56.627 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7761869031979312067/TestServlet.java
23:18:56.633 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7761869031979312067/TestServlet.java
23:18:56.638 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:18:56.639 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 2 Java files to analyze
23:18:56.639 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 1/2 ({:.1f}%) - 50.0
23:18:56.639 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10096610379695609511/Class1.java
23:18:56.639 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10096610379695609511/Class1.java
23:18:56.640 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10096610379695609511/Class1.java
23:18:56.641 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10096610379695609511/Class1.java
23:18:56.642 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 2/2 ({:.1f}%) - 100.0
23:18:56.642 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10096610379695609511/Class2.java
23:18:56.642 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10096610379695609511/Class2.java
23:18:56.642 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10096610379695609511/Class2.java
23:18:56.649 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10096610379695609511/Class2.java
23:18:56.649 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:18:56.654 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:18:56.655 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 0 Java files to analyze
23:18:56.655 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:18:56.658 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11245588645730315012/AppContextListener.java
23:18:56.658 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11245588645730315012/AppContextListener.java
23:18:56.658 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11245588645730315012/AppContextListener.java
23:18:56.663 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11245588645730315012/AppContextListener.java
23:18:56.666 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:18:56.666 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 0 Java files to analyze
23:18:56.666 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:18:56.670 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit17273630905677350280/UserService.java
23:18:56.670 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit17273630905677350280/UserService.java
23:18:56.671 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit17273630905677350280/UserService.java
23:18:56.678 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit17273630905677350280/UserService.java
23:18:56.681 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10001295575140387387/SimpleClass.java
23:18:56.681 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10001295575140387387/SimpleClass.java
23:18:56.682 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10001295575140387387/SimpleClass.java
23:18:56.688 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10001295575140387387/SimpleClass.java
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at Jul 9, 2025, 11:18:57 PM</p>
</div>
</div>
</body>
</html>
