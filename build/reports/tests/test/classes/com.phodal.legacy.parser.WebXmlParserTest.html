<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - WebXmlParserTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>WebXmlParserTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.phodal.legacy.parser.html">com.phodal.legacy.parser</a> &gt; WebXmlParserTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">9</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.104s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
<li>
<a href="#">Standard output</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">testAnalyzeEmptyDirectory()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeMultipleWebXmlFiles()</td>
<td class="success">0.013s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeSimpleWebXml()</td>
<td class="success">0.006s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeWebXmlWithContextParams()</td>
<td class="success">0.007s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeWebXmlWithErrorPages()</td>
<td class="success">0.054s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeWebXmlWithFilters()</td>
<td class="success">0.006s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeWebXmlWithListeners()</td>
<td class="success">0.005s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeWebXmlWithServlets()</td>
<td class="success">0.006s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeWebXmlWithWelcomeFiles()</td>
<td class="success">0.006s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>23:18:56.734 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3576964062227478184/web.xml
23:18:56.785 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3576964062227478184/web.xml
23:18:56.789 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3870993702043098845/web.xml
23:18:56.792 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3870993702043098845/web.xml
23:18:56.798 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeWebXmlFiles in component: WebXmlParser
23:18:56.799 [Test worker] INFO com.phodal.legacy.parser.WebXmlParser -- Found 2 web.xml files to analyze
23:18:56.800 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 1/2 ({:.1f}%) - 50.0
23:18:56.800 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7105404201622772121/sub/WEB-INF/web.xml
23:18:56.803 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7105404201622772121/sub/WEB-INF/web.xml
23:18:56.803 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 2/2 ({:.1f}%) - 100.0
23:18:56.803 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7105404201622772121/WEB-INF/web.xml
23:18:56.805 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7105404201622772121/WEB-INF/web.xml
23:18:56.806 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeWebXmlFiles in component: WebXmlParser
23:18:56.810 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit16657974000021212388/web.xml
23:18:56.813 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit16657974000021212388/web.xml
23:18:56.816 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeWebXmlFiles in component: WebXmlParser
23:18:56.816 [Test worker] INFO com.phodal.legacy.parser.WebXmlParser -- Found 0 web.xml files to analyze
23:18:56.816 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeWebXmlFiles in component: WebXmlParser
23:18:56.820 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2089787481042962654/web.xml
23:18:56.824 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2089787481042962654/web.xml
23:18:56.827 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit8827361320950465828/web.xml
23:18:56.830 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit8827361320950465828/web.xml
23:18:56.833 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit5241949190002598546/web.xml
23:18:56.836 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit5241949190002598546/web.xml
23:18:56.840 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2330526242289087122/web.xml
23:18:56.844 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2330526242289087122/web.xml
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at Jul 9, 2025, 11:18:57 PM</p>
</div>
</div>
</body>
</html>
