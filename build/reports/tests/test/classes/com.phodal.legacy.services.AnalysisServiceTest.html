<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - AnalysisServiceTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>AnalysisServiceTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.phodal.legacy.services.html">com.phodal.legacy.services</a> &gt; AnalysisServiceTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">6</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.100s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
<li>
<a href="#">Standard output</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">testAnalysisOptions()</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeComplexProject()</td>
<td class="success">0.047s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeEmptyProject()</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeNonExistentProject()</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeSimpleProject()</td>
<td class="success">0.024s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAnalyzeWithSelectiveOptions()</td>
<td class="success">0.019s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>23:06:05.200 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeProject in component: AnalysisService
23:06:05.206 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeProject in component: AnalysisService
23:06:05.206 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Starting comprehensive analysis of project: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726
23:06:05.206 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing JSP files...
23:06:05.206 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeJspFiles in component: JspAnalyzer
23:06:05.207 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 2 JSP files to analyze
23:06:05.207 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 1/2 ({:.1f}%) - 50.0
23:06:05.207 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/page1.jsp
23:06:05.208 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/page1.jsp
23:06:05.208 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/page1.jsp
23:06:05.208 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/page1.jsp
23:06:05.208 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 2/2 ({:.1f}%) - 100.0
23:06:05.208 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/page2.jsp
23:06:05.209 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/page2.jsp
23:06:05.209 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/page2.jsp
23:06:05.209 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/page2.jsp
23:06:05.210 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
23:06:05.210 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 2 JSP components
23:06:05.210 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing Java source files...
23:06:05.210 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:06:05.212 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 3 Java files to analyze
23:06:05.212 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 1/3 ({:.1f}%) - 33.33333333333333
23:06:05.212 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/src/main/java/com/example/TestClass.java
23:06:05.212 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/src/main/java/com/example/TestClass.java
23:06:05.212 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/src/main/java/com/example/TestClass.java
23:06:05.215 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/src/main/java/com/example/TestClass.java
23:06:05.216 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 2/3 ({:.1f}%) - 66.66666666666666
23:06:05.216 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/src/main/java/com/example/TestServlet.java
23:06:05.216 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/src/main/java/com/example/TestServlet.java
23:06:05.216 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/src/main/java/com/example/TestServlet.java
23:06:05.218 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/src/main/java/com/example/TestServlet.java
23:06:05.218 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 3/3 ({:.1f}%) - 100.0
23:06:05.218 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/src/main/java/com/example/TestFilter.java
23:06:05.218 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/src/main/java/com/example/TestFilter.java
23:06:05.219 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/src/main/java/com/example/TestFilter.java
23:06:05.223 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/src/main/java/com/example/TestFilter.java
23:06:05.224 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:06:05.224 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 3 Java components
23:06:05.224 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing web.xml files...
23:06:05.224 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeWebXmlFiles in component: WebXmlParser
23:06:05.226 [Test worker] INFO com.phodal.legacy.parser.WebXmlParser -- Found 1 web.xml files to analyze
23:06:05.226 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 1/1 ({:.1f}%) - 100.0
23:06:05.226 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/WEB-INF/web.xml
23:06:05.239 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/WEB-INF/web.xml
23:06:05.239 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeWebXmlFiles in component: WebXmlParser
23:06:05.239 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 web.xml components
23:06:05.239 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building JSP to bytecode mappings...
23:06:05.239 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Skipping mapping creation - no JSP or bytecode components found
23:06:05.239 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building dependency graph...
23:06:05.239 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 6 components
23:06:05.240 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:06:05.243 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeProject in component: AnalysisService
23:06:05.243 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analysis completed successfully in 36 ms
23:06:05.250 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeProject in component: AnalysisService
23:06:05.250 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Starting comprehensive analysis of project: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3298193676447074582
23:06:05.250 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing JSP files...
23:06:05.250 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeJspFiles in component: JspAnalyzer
23:06:05.251 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 0 JSP files to analyze
23:06:05.251 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
23:06:05.251 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 0 JSP components
23:06:05.251 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing Java source files...
23:06:05.251 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:06:05.251 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 0 Java files to analyze
23:06:05.251 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:06:05.251 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 0 Java components
23:06:05.251 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing web.xml files...
23:06:05.251 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeWebXmlFiles in component: WebXmlParser
23:06:05.252 [Test worker] INFO com.phodal.legacy.parser.WebXmlParser -- Found 0 web.xml files to analyze
23:06:05.252 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeWebXmlFiles in component: WebXmlParser
23:06:05.252 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 0 web.xml components
23:06:05.252 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building JSP to bytecode mappings...
23:06:05.252 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Skipping mapping creation - no JSP or bytecode components found
23:06:05.252 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building dependency graph...
23:06:05.252 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 0 components
23:06:05.252 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:06:05.253 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeProject in component: AnalysisService
23:06:05.253 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analysis completed successfully in 3 ms
23:06:05.257 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeProject in component: AnalysisService
23:06:05.257 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Starting comprehensive analysis of project: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10921968673382932053
23:06:05.257 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing JSP files...
23:06:05.257 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeJspFiles in component: JspAnalyzer
23:06:05.258 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 1 JSP files to analyze
23:06:05.258 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 1/1 ({:.1f}%) - 100.0
23:06:05.258 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10921968673382932053/index.jsp
23:06:05.258 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10921968673382932053/index.jsp
23:06:05.259 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10921968673382932053/index.jsp
23:06:05.260 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10921968673382932053/index.jsp
23:06:05.260 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
23:06:05.260 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 JSP components
23:06:05.260 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building JSP to bytecode mappings...
23:06:05.260 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Skipping mapping creation - no JSP or bytecode components found
23:06:05.260 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building dependency graph...
23:06:05.260 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 1 components
23:06:05.260 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:06:05.261 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeProject in component: AnalysisService
23:06:05.261 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analysis completed successfully in 4 ms
23:06:05.261 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeProject in component: AnalysisService
23:06:05.261 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Starting comprehensive analysis of project: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10921968673382932053
23:06:05.262 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing Java source files...
23:06:05.262 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:06:05.263 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 1 Java files to analyze
23:06:05.263 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 1/1 ({:.1f}%) - 100.0
23:06:05.263 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10921968673382932053/src/main/java/com/example/TestServlet.java
23:06:05.263 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10921968673382932053/src/main/java/com/example/TestServlet.java
23:06:05.264 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10921968673382932053/src/main/java/com/example/TestServlet.java
23:06:05.267 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10921968673382932053/src/main/java/com/example/TestServlet.java
23:06:05.267 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:06:05.267 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 Java components
23:06:05.267 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building JSP to bytecode mappings...
23:06:05.267 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Skipping mapping creation - no JSP or bytecode components found
23:06:05.267 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building dependency graph...
23:06:05.267 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 1 components
23:06:05.268 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:06:05.269 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeProject in component: AnalysisService
23:06:05.269 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analysis completed successfully in 7 ms
23:06:05.281 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeProject in component: AnalysisService
23:06:05.281 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Starting comprehensive analysis of project: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13312780554482051097
23:06:05.281 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing JSP files...
23:06:05.281 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeJspFiles in component: JspAnalyzer
23:06:05.283 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 1 JSP files to analyze
23:06:05.283 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 1/1 ({:.1f}%) - 100.0
23:06:05.283 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13312780554482051097/index.jsp
23:06:05.283 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13312780554482051097/index.jsp
23:06:05.283 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13312780554482051097/index.jsp
23:06:05.284 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13312780554482051097/index.jsp
23:06:05.284 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
23:06:05.284 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 JSP components
23:06:05.284 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing Java source files...
23:06:05.284 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:06:05.285 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 1 Java files to analyze
23:06:05.285 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 1/1 ({:.1f}%) - 100.0
23:06:05.285 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13312780554482051097/src/main/java/com/example/TestServlet.java
23:06:05.285 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13312780554482051097/src/main/java/com/example/TestServlet.java
23:06:05.286 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13312780554482051097/src/main/java/com/example/TestServlet.java
23:06:05.289 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13312780554482051097/src/main/java/com/example/TestServlet.java
23:06:05.290 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:06:05.290 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 Java components
23:06:05.290 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing web.xml files...
23:06:05.290 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: analyzeWebXmlFiles in component: WebXmlParser
23:06:05.291 [Test worker] INFO com.phodal.legacy.parser.WebXmlParser -- Found 1 web.xml files to analyze
23:06:05.292 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4ca; Progress: 1/1 ({:.1f}%) - 100.0
23:06:05.292 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13312780554482051097/WEB-INF/web.xml
23:06:05.294 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13312780554482051097/WEB-INF/web.xml
23:06:05.295 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeWebXmlFiles in component: WebXmlParser
23:06:05.295 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 web.xml components
23:06:05.295 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building JSP to bytecode mappings...
23:06:05.295 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Skipping mapping creation - no JSP or bytecode components found
23:06:05.295 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building dependency graph...
23:06:05.295 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 3 components
23:06:05.295 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:06:05.296 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeProject in component: AnalysisService
23:06:05.297 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analysis completed successfully in 15 ms
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at Jul 9, 2025, 11:06:05 PM</p>
</div>
</div>
</body>
</html>
