<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - FileUtilsTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>FileUtilsTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.phodal.legacy.utils.html">com.phodal.legacy.utils</a> &gt; FileUtilsTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">15</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.047s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
<li>
<a href="#">Standard output</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">testBackupFile()</td>
<td class="success">0.008s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testCopyFile()</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testCountFilesByExtension()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testEnsureDirectoryExists()</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testFindFilesByExtensions()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testFindJavaFiles()</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testFindJspFiles()</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testFindWebXmlFiles()</td>
<td class="success">0.006s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGetFileExtension()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGetFileNameWithoutExtension()</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGetRelativePath()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testIsJavaFile()</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testIsJspFile()</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testReadAndWriteFileContent()</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testWriteFileContentCreatesDirectories()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>23:06:05.307 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: findWebXmlFiles in component: FileUtils
23:06:05.308 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Found 1 web.xml files in /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1599878049988787406/testdir
23:06:05.309 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: findWebXmlFiles in component: FileUtils
23:06:05.314 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Writing file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15768027668282719647/test.txt
23:06:05.315 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Written file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15768027668282719647/test.txt
23:06:05.318 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Created backup: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15768027668282719647/test.txt.backup.1752073565315
23:06:05.318 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15768027668282719647/test.txt.backup.1752073565315
23:06:05.318 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15768027668282719647/test.txt.backup.1752073565315
23:06:05.329 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Writing file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit8741738388789200118/nested/deep/file.txt
23:06:05.329 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Written file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit8741738388789200118/nested/deep/file.txt
23:06:05.329 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit8741738388789200118/nested/deep/file.txt
23:06:05.330 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit8741738388789200118/nested/deep/file.txt
23:06:05.333 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: findFilesByExtensions in component: FileUtils
23:06:05.334 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Found 3 files with extensions [.tagx, .jspx, .jsp, .tag] in /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3396428859771849117/testdir
23:06:05.334 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: findFilesByExtensions in component: FileUtils
23:06:05.340 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Writing file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit279471464763406254/test.txt
23:06:05.340 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Written file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit279471464763406254/test.txt
23:06:05.340 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit279471464763406254/test.txt
23:06:05.340 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit279471464763406254/test.txt
23:06:05.343 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Writing file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit16357957431406353672/test.txt
23:06:05.343 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Written file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit16357957431406353672/test.txt
23:06:05.343 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Copying to /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit16357957431406353672/copied.txt file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit16357957431406353672/test.txt
23:06:05.344 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Copied file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit16357957431406353672/copied.txt
23:06:05.344 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit16357957431406353672/copied.txt
23:06:05.344 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit16357957431406353672/copied.txt
23:06:05.350 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: findFilesByExtensions in component: FileUtils
23:06:05.351 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Found 2 files with extensions [.java] in /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3047458872559197624/testdir
23:06:05.351 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: findFilesByExtensions in component: FileUtils
23:06:05.356 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: findFilesByExtensions in component: FileUtils
23:06:05.356 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Found 2 files with extensions [.jsp] in /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10449992777866917187/testdir
23:06:05.356 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: findFilesByExtensions in component: FileUtils
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at Jul 9, 2025, 11:06:05 PM</p>
</div>
</div>
</body>
</html>
