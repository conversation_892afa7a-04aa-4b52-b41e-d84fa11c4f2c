<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - FileUtilsTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>FileUtilsTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.phodal.legacy.utils.html">com.phodal.legacy.utils</a> &gt; FileUtilsTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">15</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.061s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
<li>
<a href="#">Standard output</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">testBackupFile()</td>
<td class="success">0.008s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testCopyFile()</td>
<td class="success">0.005s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testCountFilesByExtension()</td>
<td class="success">0.005s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testEnsureDirectoryExists()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testFindFilesByExtensions()</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testFindJavaFiles()</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testFindJspFiles()</td>
<td class="success">0.007s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testFindWebXmlFiles()</td>
<td class="success">0.006s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGetFileExtension()</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGetFileNameWithoutExtension()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGetRelativePath()</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testIsJavaFile()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testIsJspFile()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testReadAndWriteFileContent()</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testWriteFileContentCreatesDirectories()</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>23:18:56.947 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: findWebXmlFiles in component: FileUtils
23:18:56.949 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Found 1 web.xml files in /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2857656606911764609/testdir
23:18:56.949 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: findWebXmlFiles in component: FileUtils
23:18:56.953 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Writing file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit178625661262283960/test.txt
23:18:56.954 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Written file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit178625661262283960/test.txt
23:18:56.957 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Created backup: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit178625661262283960/test.txt.backup.1752074336954
23:18:56.957 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit178625661262283960/test.txt.backup.1752074336954
23:18:56.957 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit178625661262283960/test.txt.backup.1752074336954
23:18:56.967 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Writing file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2352652862319759361/nested/deep/file.txt
23:18:56.968 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Written file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2352652862319759361/nested/deep/file.txt
23:18:56.968 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2352652862319759361/nested/deep/file.txt
23:18:56.968 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2352652862319759361/nested/deep/file.txt
23:18:56.973 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: findFilesByExtensions in component: FileUtils
23:18:56.975 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Found 3 files with extensions [.tag, .jsp, .jspx, .tagx] in /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11245428360749137101/testdir
23:18:56.975 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: findFilesByExtensions in component: FileUtils
23:18:56.985 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Writing file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit54590353115950263/test.txt
23:18:56.985 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Written file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit54590353115950263/test.txt
23:18:56.985 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit54590353115950263/test.txt
23:18:56.986 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit54590353115950263/test.txt
23:18:56.990 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Writing file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3414340471095770793/test.txt
23:18:56.991 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Written file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3414340471095770793/test.txt
23:18:56.991 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Copying to /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3414340471095770793/copied.txt file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3414340471095770793/test.txt
23:18:56.992 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Copied file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3414340471095770793/copied.txt
23:18:56.992 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f4c1; Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3414340471095770793/copied.txt
23:18:56.992 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3414340471095770793/copied.txt
23:18:57.002 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: findFilesByExtensions in component: FileUtils
23:18:57.003 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Found 2 files with extensions [.java] in /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit6221690367433846416/testdir
23:18:57.003 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: findFilesByExtensions in component: FileUtils
23:18:57.009 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- &#x1f680; Starting operation: findFilesByExtensions in component: FileUtils
23:18:57.010 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Found 2 files with extensions [.jsp] in /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit9925041906616779473/testdir
23:18:57.010 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: findFilesByExtensions in component: FileUtils
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at Jul 9, 2025, 11:18:57 PM</p>
</div>
</div>
</body>
</html>
