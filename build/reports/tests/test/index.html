<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Test Summary</title>
<link href="css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="css/style.css" rel="stylesheet" type="text/css"/>
<script src="js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Test Summary</h1>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">94</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.834s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Packages</a>
</li>
<li>
<a href="#">Classes</a>
</li>
</ul>
<div class="tab">
<h2>Packages</h2>
<table>
<thead>
<tr>
<th>Package</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="success">
<a href="packages/com.phodal.legacy.html">com.phodal.legacy</a>
</td>
<td>11</td>
<td>0</td>
<td>0</td>
<td>0.332s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.phodal.legacy.examples.html">com.phodal.legacy.examples</a>
</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>0.023s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.phodal.legacy.integration.html">com.phodal.legacy.integration</a>
</td>
<td>4</td>
<td>0</td>
<td>0</td>
<td>0.020s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.phodal.legacy.mapper.html">com.phodal.legacy.mapper</a>
</td>
<td>7</td>
<td>0</td>
<td>0</td>
<td>0.011s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.phodal.legacy.model.html">com.phodal.legacy.model</a>
</td>
<td>23</td>
<td>0</td>
<td>0</td>
<td>0.019s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.phodal.legacy.parser.html">com.phodal.legacy.parser</a>
</td>
<td>27</td>
<td>0</td>
<td>0</td>
<td>0.282s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.phodal.legacy.services.html">com.phodal.legacy.services</a>
</td>
<td>6</td>
<td>0</td>
<td>0</td>
<td>0.100s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.phodal.legacy.utils.html">com.phodal.legacy.utils</a>
</td>
<td>15</td>
<td>0</td>
<td>0</td>
<td>0.047s</td>
<td class="success">100%</td>
</tr>
</tbody>
</table>
</div>
<div class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="success">
<a href="classes/com.phodal.legacy.CliAppTest.html">com.phodal.legacy.CliAppTest</a>
</td>
<td>11</td>
<td>0</td>
<td>0</td>
<td>0.332s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.phodal.legacy.examples.MappingExampleTest.html">com.phodal.legacy.examples.MappingExampleTest</a>
</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>0.023s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.phodal.legacy.integration.JspBytecodeMappingIntegrationTest.html">com.phodal.legacy.integration.JspBytecodeMappingIntegrationTest</a>
</td>
<td>4</td>
<td>0</td>
<td>0</td>
<td>0.020s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.phodal.legacy.mapper.JspBytecodeMapperTest.html">com.phodal.legacy.mapper.JspBytecodeMapperTest</a>
</td>
<td>7</td>
<td>0</td>
<td>0</td>
<td>0.011s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.phodal.legacy.model.DependencyGraphTest.html">com.phodal.legacy.model.DependencyGraphTest</a>
</td>
<td>12</td>
<td>0</td>
<td>0</td>
<td>0.014s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.phodal.legacy.model.MappingRegistryTest.html">com.phodal.legacy.model.MappingRegistryTest</a>
</td>
<td>11</td>
<td>0</td>
<td>0</td>
<td>0.005s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.phodal.legacy.parser.JavaCodeAnalyzerTest.html">com.phodal.legacy.parser.JavaCodeAnalyzerTest</a>
</td>
<td>9</td>
<td>0</td>
<td>0</td>
<td>0.156s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.phodal.legacy.parser.JspAnalyzerTest.html">com.phodal.legacy.parser.JspAnalyzerTest</a>
</td>
<td>9</td>
<td>0</td>
<td>0</td>
<td>0.033s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.phodal.legacy.parser.WebXmlParserTest.html">com.phodal.legacy.parser.WebXmlParserTest</a>
</td>
<td>9</td>
<td>0</td>
<td>0</td>
<td>0.093s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.phodal.legacy.services.AnalysisServiceTest.html">com.phodal.legacy.services.AnalysisServiceTest</a>
</td>
<td>6</td>
<td>0</td>
<td>0</td>
<td>0.100s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.phodal.legacy.utils.FileUtilsTest.html">com.phodal.legacy.utils.FileUtilsTest</a>
</td>
<td>15</td>
<td>0</td>
<td>0</td>
<td>0.047s</td>
<td class="success">100%</td>
</tr>
</tbody>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at Jul 9, 2025, 11:06:05 PM</p>
</div>
</div>
</body>
</html>
