<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.phodal.legacy.CliAppTest" tests="11" skipped="0" failures="0" errors="0" timestamp="2025-07-09T15:18:55.828Z" hostname="fdhuang.local" time="0.546">
  <properties/>
  <testcase name="testAnalyzeCommandWithOptions()" classname="com.phodal.legacy.CliAppTest" time="0.295"/>
  <testcase name="testConvertCommandWithInvalidSource()" classname="com.phodal.legacy.CliAppTest" time="0.011"/>
  <testcase name="testValidateCommandWithValidDirectory()" classname="com.phodal.legacy.CliAppTest" time="0.014"/>
  <testcase name="testVersionCommand()" classname="com.phodal.legacy.CliAppTest" time="0.011"/>
  <testcase name="testConvertCommandWithCustomOptions()" classname="com.phodal.legacy.CliAppTest" time="0.086"/>
  <testcase name="testAnalyzeCommandWithValidDirectory()" classname="com.phodal.legacy.CliAppTest" time="0.012"/>
  <testcase name="testAnalyzeCommandWithInvalidDirectory()" classname="com.phodal.legacy.CliAppTest" time="0.008"/>
  <testcase name="testConvertCommandWithValidDirectories()" classname="com.phodal.legacy.CliAppTest" time="0.047"/>
  <testcase name="testHelpCommand()" classname="com.phodal.legacy.CliAppTest" time="0.034"/>
  <testcase name="testMainCommandWithoutArguments()" classname="com.phodal.legacy.CliAppTest" time="0.007"/>
  <testcase name="testValidateCommandWithInvalidDirectory()" classname="com.phodal.legacy.CliAppTest" time="0.008"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
