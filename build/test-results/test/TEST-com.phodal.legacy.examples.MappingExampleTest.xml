<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.phodal.legacy.examples.MappingExampleTest" tests="1" skipped="0" failures="0" errors="0" timestamp="2025-07-09T15:06:04.760Z" hostname="fdhuang.local" time="0.023">
  <properties/>
  <testcase name="testMappingExampleFlow()" classname="com.phodal.legacy.examples.MappingExampleTest" time="0.023"/>
  <system-out><![CDATA[=== JSP to Bytecode Mapping Example ===

Creating example JSP components...
✓ Created 3 JSP components

Creating example bytecode components...
✓ Created 4 bytecode components (3 servlets, 1 regular class)

23:06:04.764 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: createMappings in component: JspBytecodeMapper
23:06:04.765 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Creating mappings for 3 JSP components and 4 bytecode components
23:06:04.767 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Successfully created 3 mappings out of 3 JSP components
23:06:04.768 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: createMappings in component: JspBytecodeMapper
=== Mapping Results ===
Total mappings created: 3
Total bytecode classes mapped: 3
Total dependencies mapped: 5
Total class mappings: 3
Total method mappings: 3
Total dependency mappings: 7
Total tag library mappings: 3

Individual mappings:
  /webapp/login.jsp -> org.apache.jsp.login_jsp
    Confidence: 100.00%
    Dependencies: 2
    Tag Libraries: [http://java.sun.com/jsp/jstl/core]

  /webapp/index.jsp -> org.apache.jsp.index_jsp
    Confidence: 100.00%
    Dependencies: 2
    Tag Libraries: [http://java.sun.com/jsp/jstl/core, http://java.sun.com/jsp/jstl/fmt]

  /webapp/admin/dashboard.jsp -> org.apache.jsp.admin.dashboard_jsp
    Confidence: 100.00%
    Dependencies: 3

=== Mapping Queries ===
Found mapping for /webapp/index.jsp:
  Servlet class: org.apache.jsp.index_jsp
  Dependencies: 2

Mappings using org.apache.jsp.index_jsp: 1
Mappings using java.util.List dependency: 2

All mapped bytecode classes:
  org.apache.jsp.login_jsp
  org.apache.jsp.admin.dashboard_jsp
  org.apache.jsp.index_jsp

All mapped dependencies:
  java.util.List
  com.example.AuthService
  com.example.UserService
  java.util.Map
  com.example.AdminService

=== Example Complete ===
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
