<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.phodal.legacy.integration.JspBytecodeMappingIntegrationTest" tests="4" skipped="0" failures="0" errors="0" timestamp="2025-07-09T15:06:04.785Z" hostname="fdhuang.local" time="0.021">
  <properties/>
  <testcase name="testMappingWithComplexDependencies()" classname="com.phodal.legacy.integration.JspBytecodeMappingIntegrationTest" time="0.002"/>
  <testcase name="testJspAnalysisWithMappingHints()" classname="com.phodal.legacy.integration.JspBytecodeMappingIntegrationTest" time="0.012"/>
  <testcase name="testEndToEndMappingFlow()" classname="com.phodal.legacy.integration.JspBytecodeMappingIntegrationTest" time="0.003"/>
  <testcase name="testMappingWithTagLibraries()" classname="com.phodal.legacy.integration.JspBytecodeMappingIntegrationTest" time="0.003"/>
  <system-out><![CDATA[23:06:04.786 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: createMappings in component: JspBytecodeMapper
23:06:04.787 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Creating mappings for 1 JSP components and 1 bytecode components
23:06:04.787 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Successfully created 1 mappings out of 1 JSP components
23:06:04.787 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: createMappings in component: JspBytecodeMapper
23:06:04.790 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15279727815003173345/test.jsp
23:06:04.791 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15279727815003173345/test.jsp
23:06:04.791 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15279727815003173345/test.jsp
23:06:04.796 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15279727815003173345/test.jsp
23:06:04.796 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15279727815003173345/test.jsp
23:06:04.797 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15279727815003173345/test.jsp
23:06:04.802 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: createMappings in component: JspBytecodeMapper
23:06:04.802 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Creating mappings for 2 JSP components and 3 bytecode components
23:06:04.802 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Successfully created 2 mappings out of 2 JSP components
23:06:04.802 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: createMappings in component: JspBytecodeMapper
23:06:04.805 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: createMappings in component: JspBytecodeMapper
23:06:04.805 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Creating mappings for 1 JSP components and 1 bytecode components
23:06:04.806 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Successfully created 1 mappings out of 1 JSP components
23:06:04.806 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: createMappings in component: JspBytecodeMapper
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
