<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.phodal.legacy.mapper.JspBytecodeMapperTest" tests="7" skipped="0" failures="0" errors="0" timestamp="2025-07-09T15:18:56.424Z" hostname="fdhuang.local" time="0.02">
  <properties/>
  <testcase name="testMappingRegistryOperations()" classname="com.phodal.legacy.mapper.JspBytecodeMapperTest" time="0.002"/>
  <testcase name="testCreateMappingsWithEmptyLists()" classname="com.phodal.legacy.mapper.JspBytecodeMapperTest" time="0.002"/>
  <testcase name="testMappingConfidenceCalculation()" classname="com.phodal.legacy.mapper.JspBytecodeMapperTest" time="0.001"/>
  <testcase name="testCreateMappingsWithNoMatchingServlets()" classname="com.phodal.legacy.mapper.JspBytecodeMapperTest" time="0.002"/>
  <testcase name="testCreateMappingsWithMatchingComponents()" classname="com.phodal.legacy.mapper.JspBytecodeMapperTest" time="0.001"/>
  <testcase name="testCreateMappingsWithDifferentNamingConventions()" classname="com.phodal.legacy.mapper.JspBytecodeMapperTest" time="0.002"/>
  <testcase name="testCreateMappingsWithMultipleJspFiles()" classname="com.phodal.legacy.mapper.JspBytecodeMapperTest" time="0.004"/>
  <system-out><![CDATA[23:18:56.424 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: createMappings in component: JspBytecodeMapper
23:18:56.425 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Creating mappings for 1 JSP components and 1 bytecode components
23:18:56.425 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Successfully created 1 mappings out of 1 JSP components
23:18:56.425 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: createMappings in component: JspBytecodeMapper
23:18:56.427 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: createMappings in component: JspBytecodeMapper
23:18:56.428 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Creating mappings for 0 JSP components and 0 bytecode components
23:18:56.428 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Successfully created 0 mappings out of 0 JSP components
23:18:56.428 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: createMappings in component: JspBytecodeMapper
23:18:56.430 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: createMappings in component: JspBytecodeMapper
23:18:56.431 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Creating mappings for 1 JSP components and 1 bytecode components
23:18:56.431 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Successfully created 1 mappings out of 1 JSP components
23:18:56.431 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: createMappings in component: JspBytecodeMapper
23:18:56.433 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: createMappings in component: JspBytecodeMapper
23:18:56.433 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Creating mappings for 1 JSP components and 1 bytecode components
23:18:56.433 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Successfully created 0 mappings out of 1 JSP components
23:18:56.433 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: createMappings in component: JspBytecodeMapper
23:18:56.435 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: createMappings in component: JspBytecodeMapper
23:18:56.436 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Creating mappings for 1 JSP components and 1 bytecode components
23:18:56.436 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Successfully created 1 mappings out of 1 JSP components
23:18:56.436 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: createMappings in component: JspBytecodeMapper
23:18:56.438 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: createMappings in component: JspBytecodeMapper
23:18:56.438 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Creating mappings for 1 JSP components and 1 bytecode components
23:18:56.438 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Successfully created 1 mappings out of 1 JSP components
23:18:56.438 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: createMappings in component: JspBytecodeMapper
23:18:56.440 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: createMappings in component: JspBytecodeMapper
23:18:56.440 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Creating mappings for 3 JSP components and 3 bytecode components
23:18:56.441 [Test worker] INFO com.phodal.legacy.mapper.JspBytecodeMapper -- Successfully created 3 mappings out of 3 JSP components
23:18:56.443 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: createMappings in component: JspBytecodeMapper
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
