<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.phodal.legacy.model.DependencyGraphTest" tests="12" skipped="0" failures="0" errors="0" timestamp="2025-07-09T15:18:56.446Z" hostname="fdhuang.local" time="0.029">
  <properties/>
  <testcase name="testEmptyGraph()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.001"/>
  <testcase name="testGetNodesByType()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.001"/>
  <testcase name="testNodeMigrationPriority()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.001"/>
  <testcase name="testCalculateDepths()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.001"/>
  <testcase name="testGetRootAndLeafNodes()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.002"/>
  <testcase name="testBuildSimpleDependencies()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.002"/>
  <testcase name="testAddMultipleComponents()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.001"/>
  <testcase name="testCircularDependencyDetection()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.002"/>
  <testcase name="testMigrationOrder()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.003"/>
  <testcase name="testGraphStatistics()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.002"/>
  <testcase name="testAddSingleComponent()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.002"/>
  <testcase name="testTopologicalSort()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.002"/>
  <system-out><![CDATA[23:18:56.447 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 0 components
23:18:56.447 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:18:56.450 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 3 components
23:18:56.451 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:18:56.452 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 4 components
23:18:56.453 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:18:56.454 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 5 components
23:18:56.454 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:18:56.457 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 2 components
23:18:56.457 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:18:56.462 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 3 components
23:18:56.463 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:18:56.465 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 3 components
23:18:56.465 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:18:56.469 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 4 components
23:18:56.469 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:18:56.474 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 3 components
23:18:56.474 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
