<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.phodal.legacy.model.DependencyGraphTest" tests="12" skipped="0" failures="0" errors="0" timestamp="2025-07-09T15:06:04.824Z" hostname="fdhuang.local" time="0.018">
  <properties/>
  <testcase name="testEmptyGraph()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.001"/>
  <testcase name="testGetNodesByType()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.001"/>
  <testcase name="testNodeMigrationPriority()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.002"/>
  <testcase name="testCalculateDepths()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.001"/>
  <testcase name="testGetRootAndLeafNodes()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.002"/>
  <testcase name="testBuildSimpleDependencies()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.001"/>
  <testcase name="testAddMultipleComponents()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.001"/>
  <testcase name="testCircularDependencyDetection()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.0"/>
  <testcase name="testMigrationOrder()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.002"/>
  <testcase name="testGraphStatistics()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.001"/>
  <testcase name="testAddSingleComponent()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.001"/>
  <testcase name="testTopologicalSort()" classname="com.phodal.legacy.model.DependencyGraphTest" time="0.001"/>
  <system-out><![CDATA[23:06:04.825 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 0 components
23:06:04.825 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:06:04.828 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 3 components
23:06:04.829 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:06:04.830 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 4 components
23:06:04.830 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:06:04.831 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 5 components
23:06:04.831 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:06:04.833 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 2 components
23:06:04.833 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:06:04.836 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 3 components
23:06:04.836 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:06:04.837 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 3 components
23:06:04.838 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:06:04.839 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 4 components
23:06:04.839 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:06:04.842 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 3 components
23:06:04.842 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
