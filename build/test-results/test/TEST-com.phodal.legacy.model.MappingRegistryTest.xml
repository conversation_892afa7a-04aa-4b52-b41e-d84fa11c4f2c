<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.phodal.legacy.model.MappingRegistryTest" tests="11" skipped="0" failures="0" errors="0" timestamp="2025-07-09T15:18:56.476Z" hostname="fdhuang.local" time="0.015">
  <properties/>
  <testcase name="testRegisterMultipleMappings()" classname="com.phodal.legacy.model.MappingRegistryTest" time="0.001"/>
  <testcase name="testToString()" classname="com.phodal.legacy.model.MappingRegistryTest" time="0.0"/>
  <testcase name="testIsBytecodeClassMapped()" classname="com.phodal.legacy.model.MappingRegistryTest" time="0.001"/>
  <testcase name="testGetMappedDependencies()" classname="com.phodal.legacy.model.MappingRegistryTest" time="0.0"/>
  <testcase name="testRegisterAndRetrieveMapping()" classname="com.phodal.legacy.model.MappingRegistryTest" time="0.001"/>
  <testcase name="testMappingStatistics()" classname="com.phodal.legacy.model.MappingRegistryTest" time="0.001"/>
  <testcase name="testRemoveMapping()" classname="com.phodal.legacy.model.MappingRegistryTest" time="0.001"/>
  <testcase name="testGetMappingsByBytecodeClass()" classname="com.phodal.legacy.model.MappingRegistryTest" time="0.001"/>
  <testcase name="testGetMappedBytecodeClasses()" classname="com.phodal.legacy.model.MappingRegistryTest" time="0.001"/>
  <testcase name="testClearMappings()" classname="com.phodal.legacy.model.MappingRegistryTest" time="0.001"/>
  <testcase name="testGetMappingsByDependency()" classname="com.phodal.legacy.model.MappingRegistryTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
