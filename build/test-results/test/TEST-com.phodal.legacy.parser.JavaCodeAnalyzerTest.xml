<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.phodal.legacy.parser.JavaCodeAnalyzerTest" tests="9" skipped="0" failures="0" errors="0" timestamp="2025-07-09T15:18:56.493Z" hostname="fdhuang.local" time="0.196">
  <properties/>
  <testcase name="testAnalyzeInvalidJavaFile()" classname="com.phodal.legacy.parser.JavaCodeAnalyzerTest" time="0.087"/>
  <testcase name="testAnalyzeFilterClass()" classname="com.phodal.legacy.parser.JavaCodeAnalyzerTest" time="0.043"/>
  <testcase name="testAnalyzeServletClass()" classname="com.phodal.legacy.parser.JavaCodeAnalyzerTest" time="0.01"/>
  <testcase name="testAnalyzeMultipleJavaFiles()" classname="com.phodal.legacy.parser.JavaCodeAnalyzerTest" time="0.016"/>
  <testcase name="testAnalyzeNonJavaFiles()" classname="com.phodal.legacy.parser.JavaCodeAnalyzerTest" time="0.003"/>
  <testcase name="testAnalyzeListenerClass()" classname="com.phodal.legacy.parser.JavaCodeAnalyzerTest" time="0.007"/>
  <testcase name="testAnalyzeEmptyDirectory()" classname="com.phodal.legacy.parser.JavaCodeAnalyzerTest" time="0.003"/>
  <testcase name="testAnalyzeInterfaceAndEnum()" classname="com.phodal.legacy.parser.JavaCodeAnalyzerTest" time="0.011"/>
  <testcase name="testAnalyzeSimpleJavaClass()" classname="com.phodal.legacy.parser.JavaCodeAnalyzerTest" time="0.009"/>
  <system-out><![CDATA[23:18:56.494 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit5841971925484054154/InvalidClass.java
23:18:56.494 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit5841971925484054154/InvalidClass.java
23:18:56.495 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit5841971925484054154/InvalidClass.java
23:18:56.575 [Test worker] WARN com.phodal.legacy.parser.JavaCodeAnalyzer -- Failed to parse Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit5841971925484054154/InvalidClass.java - [(line 3,col 27) Parse error. Found <EOF>, expected one of  ";" "<" "@" "abstract" "boolean" "byte" "char" "class" "default" "double" "enum" "exports" "final" "float" "int" "interface" "long" "module" "native" "non-sealed" "open" "opens" "permits" "private" "protected" "provides" "public" "record" "requires" "sealed" "short" "static" "strictfp" "synchronized" "to" "transient" "transitive" "uses" "void" "volatile" "with" "yield" "{" "}" <IDENTIFIER>
Problem stacktrace : 
  com.github.javaparser.GeneratedJavaParser.generateParseException(GeneratedJavaParser.java:14041)
  com.github.javaparser.GeneratedJavaParser.jj_consume_token(GeneratedJavaParser.java:13886)
  com.github.javaparser.GeneratedJavaParser.ClassOrInterfaceBody(GeneratedJavaParser.java:1295)
  com.github.javaparser.GeneratedJavaParser.ClassOrInterfaceDeclaration(GeneratedJavaParser.java:538)
  com.github.javaparser.GeneratedJavaParser.CompilationUnit(GeneratedJavaParser.java:156)
  com.github.javaparser.JavaParser.parse(JavaParser.java:125)
  com.github.javaparser.JavaParser.parse(JavaParser.java:305)
  com.phodal.legacy.parser.JavaCodeAnalyzer.analyzeJavaFile(JavaCodeAnalyzer.java:84)
  com.phodal.legacy.parser.JavaCodeAnalyzerTest.testAnalyzeInvalidJavaFile(JavaCodeAnalyzerTest.java:329)
  java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
  java.base/java.lang.reflect.Method.invoke(Method.java:580)
  org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)
  org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
  org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
  org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
  org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
  org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
  org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
  org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
  org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
  org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
  org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
  org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
  org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
  org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
  org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:218)
  org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
  org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:214)
  org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:139)
  org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
  org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
  org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
  org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
  java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
  org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
  org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
  org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
  org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
  java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
  org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
  org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
  org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
  org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
  org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
  org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
  org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
  org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:107)
  org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:88)
  org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:54)
  org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:67)
  org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:52)
  org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:114)
  org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:86)
  org.junit.platform.launcher.core.DefaultLauncherSession$DelegatingLauncher.execute(DefaultLauncherSession.java:86)
  org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
  org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
  org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
  org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
  java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
  java.base/java.lang.reflect.Method.invoke(Method.java:580)
  org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
  org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
  org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
  org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
  jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
  org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
  org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
  org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
  org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
  org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
  org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:122)
  org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:72)
  worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
  worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)]
23:18:56.582 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7647482750527387352/LoggingFilter.java
23:18:56.582 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7647482750527387352/LoggingFilter.java
23:18:56.582 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7647482750527387352/LoggingFilter.java
23:18:56.622 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7647482750527387352/LoggingFilter.java
23:18:56.627 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7761869031979312067/TestServlet.java
23:18:56.627 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7761869031979312067/TestServlet.java
23:18:56.627 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7761869031979312067/TestServlet.java
23:18:56.633 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7761869031979312067/TestServlet.java
23:18:56.638 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:18:56.639 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 2 Java files to analyze
23:18:56.639 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/2 ({:.1f}%) - 50.0
23:18:56.639 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10096610379695609511/Class1.java
23:18:56.639 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10096610379695609511/Class1.java
23:18:56.640 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10096610379695609511/Class1.java
23:18:56.641 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10096610379695609511/Class1.java
23:18:56.642 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 2/2 ({:.1f}%) - 100.0
23:18:56.642 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10096610379695609511/Class2.java
23:18:56.642 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10096610379695609511/Class2.java
23:18:56.642 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10096610379695609511/Class2.java
23:18:56.649 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10096610379695609511/Class2.java
23:18:56.649 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:18:56.654 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:18:56.655 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 0 Java files to analyze
23:18:56.655 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:18:56.658 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11245588645730315012/AppContextListener.java
23:18:56.658 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11245588645730315012/AppContextListener.java
23:18:56.658 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11245588645730315012/AppContextListener.java
23:18:56.663 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11245588645730315012/AppContextListener.java
23:18:56.666 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:18:56.666 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 0 Java files to analyze
23:18:56.666 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:18:56.670 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit17273630905677350280/UserService.java
23:18:56.670 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit17273630905677350280/UserService.java
23:18:56.671 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit17273630905677350280/UserService.java
23:18:56.678 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit17273630905677350280/UserService.java
23:18:56.681 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10001295575140387387/SimpleClass.java
23:18:56.681 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10001295575140387387/SimpleClass.java
23:18:56.682 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10001295575140387387/SimpleClass.java
23:18:56.688 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10001295575140387387/SimpleClass.java
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
