<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.phodal.legacy.parser.JavaCodeAnalyzerTest" tests="9" skipped="0" failures="0" errors="0" timestamp="2025-07-09T15:06:04.855Z" hostname="fdhuang.local" time="0.159">
  <properties/>
  <testcase name="testAnalyzeInvalidJavaFile()" classname="com.phodal.legacy.parser.JavaCodeAnalyzerTest" time="0.07"/>
  <testcase name="testAnalyzeFilterClass()" classname="com.phodal.legacy.parser.JavaCodeAnalyzerTest" time="0.032"/>
  <testcase name="testAnalyzeServletClass()" classname="com.phodal.legacy.parser.JavaCodeAnalyzerTest" time="0.01"/>
  <testcase name="testAnalyzeMultipleJavaFiles()" classname="com.phodal.legacy.parser.JavaCodeAnalyzerTest" time="0.015"/>
  <testcase name="testAnalyzeNonJavaFiles()" classname="com.phodal.legacy.parser.JavaCodeAnalyzerTest" time="0.004"/>
  <testcase name="testAnalyzeListenerClass()" classname="com.phodal.legacy.parser.JavaCodeAnalyzerTest" time="0.008"/>
  <testcase name="testAnalyzeEmptyDirectory()" classname="com.phodal.legacy.parser.JavaCodeAnalyzerTest" time="0.002"/>
  <testcase name="testAnalyzeInterfaceAndEnum()" classname="com.phodal.legacy.parser.JavaCodeAnalyzerTest" time="0.008"/>
  <testcase name="testAnalyzeSimpleJavaClass()" classname="com.phodal.legacy.parser.JavaCodeAnalyzerTest" time="0.007"/>
  <system-out><![CDATA[23:06:04.857 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit14666431697964014895/InvalidClass.java
23:06:04.857 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit14666431697964014895/InvalidClass.java
23:06:04.857 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit14666431697964014895/InvalidClass.java
23:06:04.921 [Test worker] WARN com.phodal.legacy.parser.JavaCodeAnalyzer -- Failed to parse Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit14666431697964014895/InvalidClass.java - [(line 3,col 27) Parse error. Found <EOF>, expected one of  ";" "<" "@" "abstract" "boolean" "byte" "char" "class" "default" "double" "enum" "exports" "final" "float" "int" "interface" "long" "module" "native" "non-sealed" "open" "opens" "permits" "private" "protected" "provides" "public" "record" "requires" "sealed" "short" "static" "strictfp" "synchronized" "to" "transient" "transitive" "uses" "void" "volatile" "with" "yield" "{" "}" <IDENTIFIER>
Problem stacktrace : 
  com.github.javaparser.GeneratedJavaParser.generateParseException(GeneratedJavaParser.java:14041)
  com.github.javaparser.GeneratedJavaParser.jj_consume_token(GeneratedJavaParser.java:13886)
  com.github.javaparser.GeneratedJavaParser.ClassOrInterfaceBody(GeneratedJavaParser.java:1295)
  com.github.javaparser.GeneratedJavaParser.ClassOrInterfaceDeclaration(GeneratedJavaParser.java:538)
  com.github.javaparser.GeneratedJavaParser.CompilationUnit(GeneratedJavaParser.java:156)
  com.github.javaparser.JavaParser.parse(JavaParser.java:125)
  com.github.javaparser.JavaParser.parse(JavaParser.java:305)
  com.phodal.legacy.parser.JavaCodeAnalyzer.analyzeJavaFile(JavaCodeAnalyzer.java:84)
  com.phodal.legacy.parser.JavaCodeAnalyzerTest.testAnalyzeInvalidJavaFile(JavaCodeAnalyzerTest.java:329)
  java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
  java.base/java.lang.reflect.Method.invoke(Method.java:580)
  org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)
  org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
  org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
  org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
  org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
  org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
  org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
  org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
  org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
  org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
  org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
  org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
  org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
  org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
  org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:218)
  org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
  org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:214)
  org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:139)
  org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
  org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
  org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
  org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
  java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
  org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
  org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
  org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
  org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
  java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
  org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
  org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
  org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
  org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
  org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
  org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
  org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
  org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
  org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:107)
  org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:88)
  org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:54)
  org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:67)
  org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:52)
  org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:114)
  org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:86)
  org.junit.platform.launcher.core.DefaultLauncherSession$DelegatingLauncher.execute(DefaultLauncherSession.java:86)
  org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
  org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
  org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
  org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
  java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
  java.base/java.lang.reflect.Method.invoke(Method.java:580)
  org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
  org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
  org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
  org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
  jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
  org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
  org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
  org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
  org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
  org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
  org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:122)
  org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:72)
  worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
  worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)]
23:06:04.927 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13262219315459198090/LoggingFilter.java
23:06:04.927 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13262219315459198090/LoggingFilter.java
23:06:04.928 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13262219315459198090/LoggingFilter.java
23:06:04.955 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13262219315459198090/LoggingFilter.java
23:06:04.960 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit6363847266015842190/TestServlet.java
23:06:04.960 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit6363847266015842190/TestServlet.java
23:06:04.961 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit6363847266015842190/TestServlet.java
23:06:04.967 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit6363847266015842190/TestServlet.java
23:06:04.971 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:06:04.971 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 2 Java files to analyze
23:06:04.971 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/2 ({:.1f}%) - 50.0
23:06:04.972 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7778082223367021192/Class1.java
23:06:04.972 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7778082223367021192/Class1.java
23:06:04.972 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7778082223367021192/Class1.java
23:06:04.974 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7778082223367021192/Class1.java
23:06:04.974 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 2/2 ({:.1f}%) - 100.0
23:06:04.974 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7778082223367021192/Class2.java
23:06:04.974 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7778082223367021192/Class2.java
23:06:04.975 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7778082223367021192/Class2.java
23:06:04.981 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7778082223367021192/Class2.java
23:06:04.982 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:06:04.986 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:06:04.986 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 0 Java files to analyze
23:06:04.987 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:06:04.990 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit6268369553797800032/AppContextListener.java
23:06:04.990 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit6268369553797800032/AppContextListener.java
23:06:04.990 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit6268369553797800032/AppContextListener.java
23:06:04.995 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit6268369553797800032/AppContextListener.java
23:06:04.998 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:06:04.998 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 0 Java files to analyze
23:06:04.998 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:06:05.000 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit602641618184149451/UserService.java
23:06:05.000 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit602641618184149451/UserService.java
23:06:05.001 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit602641618184149451/UserService.java
23:06:05.006 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit602641618184149451/UserService.java
23:06:05.009 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit583367687900140235/SimpleClass.java
23:06:05.009 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit583367687900140235/SimpleClass.java
23:06:05.009 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit583367687900140235/SimpleClass.java
23:06:05.014 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit583367687900140235/SimpleClass.java
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
