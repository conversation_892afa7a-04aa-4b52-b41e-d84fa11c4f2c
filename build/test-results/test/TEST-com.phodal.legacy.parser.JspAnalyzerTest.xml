<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.phodal.legacy.parser.JspAnalyzerTest" tests="9" skipped="0" failures="0" errors="0" timestamp="2025-07-09T15:06:05.016Z" hostname="fdhuang.local" time="0.037">
  <properties/>
  <testcase name="testAnalyzeJspxFile()" classname="com.phodal.legacy.parser.JspAnalyzerTest" time="0.006"/>
  <testcase name="testAnalyzeMultipleJspFiles()" classname="com.phodal.legacy.parser.JspAnalyzerTest" time="0.006"/>
  <testcase name="testAnalyzeJspWithErrorPage()" classname="com.phodal.legacy.parser.JspAnalyzerTest" time="0.003"/>
  <testcase name="testAnalyzeJspWithSessionDisabled()" classname="com.phodal.legacy.parser.JspAnalyzerTest" time="0.003"/>
  <testcase name="testAnalyzeJspWithDeclarations()" classname="com.phodal.legacy.parser.JspAnalyzerTest" time="0.004"/>
  <testcase name="testAnalyzeJspWithImports()" classname="com.phodal.legacy.parser.JspAnalyzerTest" time="0.003"/>
  <testcase name="testAnalyzeNonJspFiles()" classname="com.phodal.legacy.parser.JspAnalyzerTest" time="0.002"/>
  <testcase name="testAnalyzeSimpleJspFile()" classname="com.phodal.legacy.parser.JspAnalyzerTest" time="0.004"/>
  <testcase name="testAnalyzeEmptyDirectory()" classname="com.phodal.legacy.parser.JspAnalyzerTest" time="0.002"/>
  <system-out><![CDATA[23:06:05.018 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJspFiles in component: JspAnalyzer
23:06:05.018 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 1 JSP files to analyze
23:06:05.019 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/1 ({:.1f}%) - 100.0
23:06:05.019 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11764090175029509344/test.jspx
23:06:05.020 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11764090175029509344/test.jspx
23:06:05.020 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11764090175029509344/test.jspx
23:06:05.021 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11764090175029509344/test.jspx
23:06:05.021 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
23:06:05.025 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJspFiles in component: JspAnalyzer
23:06:05.025 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 2 JSP files to analyze
23:06:05.025 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/2 ({:.1f}%) - 50.0
23:06:05.025 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit12014154258251414961/page1.jsp
23:06:05.026 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit12014154258251414961/page1.jsp
23:06:05.026 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit12014154258251414961/page1.jsp
23:06:05.026 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit12014154258251414961/page1.jsp
23:06:05.026 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 2/2 ({:.1f}%) - 100.0
23:06:05.026 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit12014154258251414961/page2.jsp
23:06:05.026 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit12014154258251414961/page2.jsp
23:06:05.027 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit12014154258251414961/page2.jsp
23:06:05.027 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit12014154258251414961/page2.jsp
23:06:05.027 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
23:06:05.030 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7790390654907350754/main.jsp
23:06:05.031 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7790390654907350754/main.jsp
23:06:05.031 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7790390654907350754/main.jsp
23:06:05.031 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7790390654907350754/main.jsp
23:06:05.034 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit582471656305573608/nosession.jsp
23:06:05.034 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit582471656305573608/nosession.jsp
23:06:05.034 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit582471656305573608/nosession.jsp
23:06:05.035 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit582471656305573608/nosession.jsp
23:06:05.038 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1000354577377504517/declarations.jsp
23:06:05.038 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1000354577377504517/declarations.jsp
23:06:05.038 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1000354577377504517/declarations.jsp
23:06:05.039 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1000354577377504517/declarations.jsp
23:06:05.042 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15762529444769715093/complex.jsp
23:06:05.042 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15762529444769715093/complex.jsp
23:06:05.042 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15762529444769715093/complex.jsp
23:06:05.043 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15762529444769715093/complex.jsp
23:06:05.046 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJspFiles in component: JspAnalyzer
23:06:05.046 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 0 JSP files to analyze
23:06:05.046 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
23:06:05.048 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit6088324801359969672/test.jsp
23:06:05.048 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit6088324801359969672/test.jsp
23:06:05.048 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit6088324801359969672/test.jsp
23:06:05.049 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit6088324801359969672/test.jsp
23:06:05.053 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJspFiles in component: JspAnalyzer
23:06:05.053 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 0 JSP files to analyze
23:06:05.053 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
