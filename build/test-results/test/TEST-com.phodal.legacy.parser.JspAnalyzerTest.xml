<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.phodal.legacy.parser.JspAnalyzerTest" tests="9" skipped="0" failures="0" errors="0" timestamp="2025-07-09T15:18:56.692Z" hostname="fdhuang.local" time="0.039">
  <properties/>
  <testcase name="testAnalyzeJspxFile()" classname="com.phodal.legacy.parser.JspAnalyzerTest" time="0.005"/>
  <testcase name="testAnalyzeMultipleJspFiles()" classname="com.phodal.legacy.parser.JspAnalyzerTest" time="0.006"/>
  <testcase name="testAnalyzeJspWithErrorPage()" classname="com.phodal.legacy.parser.JspAnalyzerTest" time="0.003"/>
  <testcase name="testAnalyzeJspWithSessionDisabled()" classname="com.phodal.legacy.parser.JspAnalyzerTest" time="0.004"/>
  <testcase name="testAnalyzeJspWithDeclarations()" classname="com.phodal.legacy.parser.JspAnalyzerTest" time="0.003"/>
  <testcase name="testAnalyzeJspWithImports()" classname="com.phodal.legacy.parser.JspAnalyzerTest" time="0.004"/>
  <testcase name="testAnalyzeNonJspFiles()" classname="com.phodal.legacy.parser.JspAnalyzerTest" time="0.003"/>
  <testcase name="testAnalyzeSimpleJspFile()" classname="com.phodal.legacy.parser.JspAnalyzerTest" time="0.004"/>
  <testcase name="testAnalyzeEmptyDirectory()" classname="com.phodal.legacy.parser.JspAnalyzerTest" time="0.002"/>
  <system-out><![CDATA[23:18:56.693 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJspFiles in component: JspAnalyzer
23:18:56.694 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 1 JSP files to analyze
23:18:56.695 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/1 ({:.1f}%) - 100.0
23:18:56.695 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13266190498587445790/test.jspx
23:18:56.695 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13266190498587445790/test.jspx
23:18:56.695 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13266190498587445790/test.jspx
23:18:56.696 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13266190498587445790/test.jspx
23:18:56.696 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
23:18:56.699 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJspFiles in component: JspAnalyzer
23:18:56.700 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 2 JSP files to analyze
23:18:56.700 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/2 ({:.1f}%) - 50.0
23:18:56.700 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7598196025249038190/page1.jsp
23:18:56.700 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7598196025249038190/page1.jsp
23:18:56.701 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7598196025249038190/page1.jsp
23:18:56.701 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7598196025249038190/page1.jsp
23:18:56.701 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 2/2 ({:.1f}%) - 100.0
23:18:56.701 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7598196025249038190/page2.jsp
23:18:56.701 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7598196025249038190/page2.jsp
23:18:56.701 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7598196025249038190/page2.jsp
23:18:56.702 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7598196025249038190/page2.jsp
23:18:56.702 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
23:18:56.706 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit5658704295402080315/main.jsp
23:18:56.706 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit5658704295402080315/main.jsp
23:18:56.706 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit5658704295402080315/main.jsp
23:18:56.707 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit5658704295402080315/main.jsp
23:18:56.710 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit8152107423042177034/nosession.jsp
23:18:56.710 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit8152107423042177034/nosession.jsp
23:18:56.710 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit8152107423042177034/nosession.jsp
23:18:56.711 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit8152107423042177034/nosession.jsp
23:18:56.714 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3744896126634571533/declarations.jsp
23:18:56.714 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3744896126634571533/declarations.jsp
23:18:56.714 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3744896126634571533/declarations.jsp
23:18:56.715 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3744896126634571533/declarations.jsp
23:18:56.718 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3315596584049618447/complex.jsp
23:18:56.718 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3315596584049618447/complex.jsp
23:18:56.718 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3315596584049618447/complex.jsp
23:18:56.720 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3315596584049618447/complex.jsp
23:18:56.723 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJspFiles in component: JspAnalyzer
23:18:56.724 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 0 JSP files to analyze
23:18:56.724 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
23:18:56.726 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1367702371540862389/test.jsp
23:18:56.727 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1367702371540862389/test.jsp
23:18:56.727 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1367702371540862389/test.jsp
23:18:56.727 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1367702371540862389/test.jsp
23:18:56.730 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJspFiles in component: JspAnalyzer
23:18:56.730 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 0 JSP files to analyze
23:18:56.730 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
