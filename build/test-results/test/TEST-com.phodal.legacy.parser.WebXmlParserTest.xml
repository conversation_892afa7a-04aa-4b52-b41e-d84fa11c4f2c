<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.phodal.legacy.parser.WebXmlParserTest" tests="9" skipped="0" failures="0" errors="0" timestamp="2025-07-09T15:18:56.733Z" hostname="fdhuang.local" time="0.113">
  <properties/>
  <testcase name="testAnalyzeWebXmlWithErrorPages()" classname="com.phodal.legacy.parser.WebXmlParserTest" time="0.054"/>
  <testcase name="testAnalyzeSimpleWebXml()" classname="com.phodal.legacy.parser.WebXmlParserTest" time="0.006"/>
  <testcase name="testAnalyzeMultipleWebXmlFiles()" classname="com.phodal.legacy.parser.WebXmlParserTest" time="0.013"/>
  <testcase name="testAnalyzeWebXmlWithServlets()" classname="com.phodal.legacy.parser.WebXmlParserTest" time="0.006"/>
  <testcase name="testAnalyzeEmptyDirectory()" classname="com.phodal.legacy.parser.WebXmlParserTest" time="0.001"/>
  <testcase name="testAnalyzeWebXmlWithFilters()" classname="com.phodal.legacy.parser.WebXmlParserTest" time="0.006"/>
  <testcase name="testAnalyzeWebXmlWithListeners()" classname="com.phodal.legacy.parser.WebXmlParserTest" time="0.005"/>
  <testcase name="testAnalyzeWebXmlWithWelcomeFiles()" classname="com.phodal.legacy.parser.WebXmlParserTest" time="0.006"/>
  <testcase name="testAnalyzeWebXmlWithContextParams()" classname="com.phodal.legacy.parser.WebXmlParserTest" time="0.007"/>
  <system-out><![CDATA[23:18:56.734 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3576964062227478184/web.xml
23:18:56.785 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3576964062227478184/web.xml
23:18:56.789 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3870993702043098845/web.xml
23:18:56.792 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3870993702043098845/web.xml
23:18:56.798 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeWebXmlFiles in component: WebXmlParser
23:18:56.799 [Test worker] INFO com.phodal.legacy.parser.WebXmlParser -- Found 2 web.xml files to analyze
23:18:56.800 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/2 ({:.1f}%) - 50.0
23:18:56.800 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7105404201622772121/sub/WEB-INF/web.xml
23:18:56.803 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7105404201622772121/sub/WEB-INF/web.xml
23:18:56.803 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 2/2 ({:.1f}%) - 100.0
23:18:56.803 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7105404201622772121/WEB-INF/web.xml
23:18:56.805 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit7105404201622772121/WEB-INF/web.xml
23:18:56.806 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeWebXmlFiles in component: WebXmlParser
23:18:56.810 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit16657974000021212388/web.xml
23:18:56.813 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit16657974000021212388/web.xml
23:18:56.816 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeWebXmlFiles in component: WebXmlParser
23:18:56.816 [Test worker] INFO com.phodal.legacy.parser.WebXmlParser -- Found 0 web.xml files to analyze
23:18:56.816 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeWebXmlFiles in component: WebXmlParser
23:18:56.820 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2089787481042962654/web.xml
23:18:56.824 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2089787481042962654/web.xml
23:18:56.827 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit8827361320950465828/web.xml
23:18:56.830 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit8827361320950465828/web.xml
23:18:56.833 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit5241949190002598546/web.xml
23:18:56.836 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit5241949190002598546/web.xml
23:18:56.840 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2330526242289087122/web.xml
23:18:56.844 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2330526242289087122/web.xml
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
