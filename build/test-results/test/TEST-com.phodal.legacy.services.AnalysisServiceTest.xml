<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.phodal.legacy.services.AnalysisServiceTest" tests="6" skipped="0" failures="0" errors="0" timestamp="2025-07-09T15:06:05.197Z" hostname="fdhuang.local" time="0.103">
  <properties/>
  <testcase name="testAnalyzeNonExistentProject()" classname="com.phodal.legacy.services.AnalysisServiceTest" time="0.004"/>
  <testcase name="testAnalyzeComplexProject()" classname="com.phodal.legacy.services.AnalysisServiceTest" time="0.047"/>
  <testcase name="testAnalyzeEmptyProject()" classname="com.phodal.legacy.services.AnalysisServiceTest" time="0.004"/>
  <testcase name="testAnalyzeWithSelectiveOptions()" classname="com.phodal.legacy.services.AnalysisServiceTest" time="0.019"/>
  <testcase name="testAnalysisOptions()" classname="com.phodal.legacy.services.AnalysisServiceTest" time="0.002"/>
  <testcase name="testAnalyzeSimpleProject()" classname="com.phodal.legacy.services.AnalysisServiceTest" time="0.024"/>
  <system-out><![CDATA[23:06:05.200 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeProject in component: AnalysisService
23:06:05.206 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeProject in component: AnalysisService
23:06:05.206 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Starting comprehensive analysis of project: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726
23:06:05.206 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing JSP files...
23:06:05.206 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJspFiles in component: JspAnalyzer
23:06:05.207 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 2 JSP files to analyze
23:06:05.207 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/2 ({:.1f}%) - 50.0
23:06:05.207 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/page1.jsp
23:06:05.208 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/page1.jsp
23:06:05.208 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/page1.jsp
23:06:05.208 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/page1.jsp
23:06:05.208 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 2/2 ({:.1f}%) - 100.0
23:06:05.208 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/page2.jsp
23:06:05.209 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/page2.jsp
23:06:05.209 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/page2.jsp
23:06:05.209 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/page2.jsp
23:06:05.210 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
23:06:05.210 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 2 JSP components
23:06:05.210 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing Java source files...
23:06:05.210 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:06:05.212 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 3 Java files to analyze
23:06:05.212 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/3 ({:.1f}%) - 33.33333333333333
23:06:05.212 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/src/main/java/com/example/TestClass.java
23:06:05.212 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/src/main/java/com/example/TestClass.java
23:06:05.212 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/src/main/java/com/example/TestClass.java
23:06:05.215 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/src/main/java/com/example/TestClass.java
23:06:05.216 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 2/3 ({:.1f}%) - 66.66666666666666
23:06:05.216 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/src/main/java/com/example/TestServlet.java
23:06:05.216 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/src/main/java/com/example/TestServlet.java
23:06:05.216 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/src/main/java/com/example/TestServlet.java
23:06:05.218 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/src/main/java/com/example/TestServlet.java
23:06:05.218 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 3/3 ({:.1f}%) - 100.0
23:06:05.218 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/src/main/java/com/example/TestFilter.java
23:06:05.218 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/src/main/java/com/example/TestFilter.java
23:06:05.219 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/src/main/java/com/example/TestFilter.java
23:06:05.223 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/src/main/java/com/example/TestFilter.java
23:06:05.224 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:06:05.224 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 3 Java components
23:06:05.224 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing web.xml files...
23:06:05.224 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeWebXmlFiles in component: WebXmlParser
23:06:05.226 [Test worker] INFO com.phodal.legacy.parser.WebXmlParser -- Found 1 web.xml files to analyze
23:06:05.226 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/1 ({:.1f}%) - 100.0
23:06:05.226 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/WEB-INF/web.xml
23:06:05.239 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13301898578570430726/WEB-INF/web.xml
23:06:05.239 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeWebXmlFiles in component: WebXmlParser
23:06:05.239 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 web.xml components
23:06:05.239 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building JSP to bytecode mappings...
23:06:05.239 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Skipping mapping creation - no JSP or bytecode components found
23:06:05.239 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building dependency graph...
23:06:05.239 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 6 components
23:06:05.240 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:06:05.243 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeProject in component: AnalysisService
23:06:05.243 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analysis completed successfully in 36 ms
23:06:05.250 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeProject in component: AnalysisService
23:06:05.250 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Starting comprehensive analysis of project: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3298193676447074582
23:06:05.250 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing JSP files...
23:06:05.250 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJspFiles in component: JspAnalyzer
23:06:05.251 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 0 JSP files to analyze
23:06:05.251 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
23:06:05.251 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 0 JSP components
23:06:05.251 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing Java source files...
23:06:05.251 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:06:05.251 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 0 Java files to analyze
23:06:05.251 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:06:05.251 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 0 Java components
23:06:05.251 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing web.xml files...
23:06:05.251 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeWebXmlFiles in component: WebXmlParser
23:06:05.252 [Test worker] INFO com.phodal.legacy.parser.WebXmlParser -- Found 0 web.xml files to analyze
23:06:05.252 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeWebXmlFiles in component: WebXmlParser
23:06:05.252 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 0 web.xml components
23:06:05.252 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building JSP to bytecode mappings...
23:06:05.252 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Skipping mapping creation - no JSP or bytecode components found
23:06:05.252 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building dependency graph...
23:06:05.252 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 0 components
23:06:05.252 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:06:05.253 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeProject in component: AnalysisService
23:06:05.253 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analysis completed successfully in 3 ms
23:06:05.257 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeProject in component: AnalysisService
23:06:05.257 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Starting comprehensive analysis of project: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10921968673382932053
23:06:05.257 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing JSP files...
23:06:05.257 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJspFiles in component: JspAnalyzer
23:06:05.258 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 1 JSP files to analyze
23:06:05.258 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/1 ({:.1f}%) - 100.0
23:06:05.258 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10921968673382932053/index.jsp
23:06:05.258 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10921968673382932053/index.jsp
23:06:05.259 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10921968673382932053/index.jsp
23:06:05.260 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10921968673382932053/index.jsp
23:06:05.260 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
23:06:05.260 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 JSP components
23:06:05.260 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building JSP to bytecode mappings...
23:06:05.260 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Skipping mapping creation - no JSP or bytecode components found
23:06:05.260 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building dependency graph...
23:06:05.260 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 1 components
23:06:05.260 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:06:05.261 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeProject in component: AnalysisService
23:06:05.261 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analysis completed successfully in 4 ms
23:06:05.261 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeProject in component: AnalysisService
23:06:05.261 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Starting comprehensive analysis of project: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10921968673382932053
23:06:05.262 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing Java source files...
23:06:05.262 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:06:05.263 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 1 Java files to analyze
23:06:05.263 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/1 ({:.1f}%) - 100.0
23:06:05.263 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10921968673382932053/src/main/java/com/example/TestServlet.java
23:06:05.263 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10921968673382932053/src/main/java/com/example/TestServlet.java
23:06:05.264 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10921968673382932053/src/main/java/com/example/TestServlet.java
23:06:05.267 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10921968673382932053/src/main/java/com/example/TestServlet.java
23:06:05.267 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:06:05.267 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 Java components
23:06:05.267 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building JSP to bytecode mappings...
23:06:05.267 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Skipping mapping creation - no JSP or bytecode components found
23:06:05.267 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building dependency graph...
23:06:05.267 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 1 components
23:06:05.268 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:06:05.269 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeProject in component: AnalysisService
23:06:05.269 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analysis completed successfully in 7 ms
23:06:05.281 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeProject in component: AnalysisService
23:06:05.281 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Starting comprehensive analysis of project: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13312780554482051097
23:06:05.281 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing JSP files...
23:06:05.281 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJspFiles in component: JspAnalyzer
23:06:05.283 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 1 JSP files to analyze
23:06:05.283 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/1 ({:.1f}%) - 100.0
23:06:05.283 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13312780554482051097/index.jsp
23:06:05.283 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13312780554482051097/index.jsp
23:06:05.283 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13312780554482051097/index.jsp
23:06:05.284 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13312780554482051097/index.jsp
23:06:05.284 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
23:06:05.284 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 JSP components
23:06:05.284 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing Java source files...
23:06:05.284 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:06:05.285 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 1 Java files to analyze
23:06:05.285 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/1 ({:.1f}%) - 100.0
23:06:05.285 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13312780554482051097/src/main/java/com/example/TestServlet.java
23:06:05.285 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13312780554482051097/src/main/java/com/example/TestServlet.java
23:06:05.286 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13312780554482051097/src/main/java/com/example/TestServlet.java
23:06:05.289 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13312780554482051097/src/main/java/com/example/TestServlet.java
23:06:05.290 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:06:05.290 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 Java components
23:06:05.290 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing web.xml files...
23:06:05.290 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeWebXmlFiles in component: WebXmlParser
23:06:05.291 [Test worker] INFO com.phodal.legacy.parser.WebXmlParser -- Found 1 web.xml files to analyze
23:06:05.292 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/1 ({:.1f}%) - 100.0
23:06:05.292 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13312780554482051097/WEB-INF/web.xml
23:06:05.294 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13312780554482051097/WEB-INF/web.xml
23:06:05.295 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeWebXmlFiles in component: WebXmlParser
23:06:05.295 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 web.xml components
23:06:05.295 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building JSP to bytecode mappings...
23:06:05.295 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Skipping mapping creation - no JSP or bytecode components found
23:06:05.295 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building dependency graph...
23:06:05.295 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 3 components
23:06:05.295 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:06:05.296 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeProject in component: AnalysisService
23:06:05.297 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analysis completed successfully in 15 ms
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
