<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.phodal.legacy.services.AnalysisServiceTest" tests="6" skipped="0" failures="0" errors="0" timestamp="2025-07-09T15:18:56.849Z" hostname="fdhuang.local" time="0.089">
  <properties/>
  <testcase name="testAnalyzeNonExistentProject()" classname="com.phodal.legacy.services.AnalysisServiceTest" time="0.005"/>
  <testcase name="testAnalyzeComplexProject()" classname="com.phodal.legacy.services.AnalysisServiceTest" time="0.034"/>
  <testcase name="testAnalyzeEmptyProject()" classname="com.phodal.legacy.services.AnalysisServiceTest" time="0.005"/>
  <testcase name="testAnalyzeWithSelectiveOptions()" classname="com.phodal.legacy.services.AnalysisServiceTest" time="0.019"/>
  <testcase name="testAnalysisOptions()" classname="com.phodal.legacy.services.AnalysisServiceTest" time="0.002"/>
  <testcase name="testAnalyzeSimpleProject()" classname="com.phodal.legacy.services.AnalysisServiceTest" time="0.021"/>
  <system-out><![CDATA[23:18:56.852 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeProject in component: AnalysisService
23:18:56.858 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeProject in component: AnalysisService
23:18:56.858 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Starting comprehensive analysis of project: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit14629986668563164253
23:18:56.858 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing JSP files...
23:18:56.858 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJspFiles in component: JspAnalyzer
23:18:56.859 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 2 JSP files to analyze
23:18:56.860 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/2 ({:.1f}%) - 50.0
23:18:56.860 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit14629986668563164253/page1.jsp
23:18:56.860 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit14629986668563164253/page1.jsp
23:18:56.860 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit14629986668563164253/page1.jsp
23:18:56.860 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit14629986668563164253/page1.jsp
23:18:56.861 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 2/2 ({:.1f}%) - 100.0
23:18:56.861 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit14629986668563164253/page2.jsp
23:18:56.861 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit14629986668563164253/page2.jsp
23:18:56.861 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit14629986668563164253/page2.jsp
23:18:56.861 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit14629986668563164253/page2.jsp
23:18:56.861 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
23:18:56.861 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 2 JSP components
23:18:56.861 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing Java source files...
23:18:56.861 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:18:56.863 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 3 Java files to analyze
23:18:56.863 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/3 ({:.1f}%) - 33.33333333333333
23:18:56.863 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit14629986668563164253/src/main/java/com/example/TestClass.java
23:18:56.863 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit14629986668563164253/src/main/java/com/example/TestClass.java
23:18:56.863 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit14629986668563164253/src/main/java/com/example/TestClass.java
23:18:56.866 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit14629986668563164253/src/main/java/com/example/TestClass.java
23:18:56.866 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 2/3 ({:.1f}%) - 66.66666666666666
23:18:56.866 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit14629986668563164253/src/main/java/com/example/TestServlet.java
23:18:56.866 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit14629986668563164253/src/main/java/com/example/TestServlet.java
23:18:56.867 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit14629986668563164253/src/main/java/com/example/TestServlet.java
23:18:56.868 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit14629986668563164253/src/main/java/com/example/TestServlet.java
23:18:56.868 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 3/3 ({:.1f}%) - 100.0
23:18:56.869 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit14629986668563164253/src/main/java/com/example/TestFilter.java
23:18:56.869 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit14629986668563164253/src/main/java/com/example/TestFilter.java
23:18:56.869 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit14629986668563164253/src/main/java/com/example/TestFilter.java
23:18:56.871 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit14629986668563164253/src/main/java/com/example/TestFilter.java
23:18:56.872 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:18:56.872 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 3 Java components
23:18:56.872 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing web.xml files...
23:18:56.872 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeWebXmlFiles in component: WebXmlParser
23:18:56.875 [Test worker] INFO com.phodal.legacy.parser.WebXmlParser -- Found 1 web.xml files to analyze
23:18:56.875 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/1 ({:.1f}%) - 100.0
23:18:56.875 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit14629986668563164253/WEB-INF/web.xml
23:18:56.879 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit14629986668563164253/WEB-INF/web.xml
23:18:56.879 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeWebXmlFiles in component: WebXmlParser
23:18:56.879 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 web.xml components
23:18:56.880 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building JSP to bytecode mappings...
23:18:56.880 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Skipping mapping creation - no JSP or bytecode components found
23:18:56.880 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building dependency graph...
23:18:56.880 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 6 components
23:18:56.880 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:18:56.882 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeProject in component: AnalysisService
23:18:56.882 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analysis completed successfully in 24 ms
23:18:56.890 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeProject in component: AnalysisService
23:18:56.890 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Starting comprehensive analysis of project: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13391919135253955698
23:18:56.890 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing JSP files...
23:18:56.890 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJspFiles in component: JspAnalyzer
23:18:56.890 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 0 JSP files to analyze
23:18:56.890 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
23:18:56.890 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 0 JSP components
23:18:56.890 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing Java source files...
23:18:56.890 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:18:56.891 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 0 Java files to analyze
23:18:56.891 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:18:56.891 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 0 Java components
23:18:56.891 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing web.xml files...
23:18:56.891 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeWebXmlFiles in component: WebXmlParser
23:18:56.891 [Test worker] INFO com.phodal.legacy.parser.WebXmlParser -- Found 0 web.xml files to analyze
23:18:56.891 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeWebXmlFiles in component: WebXmlParser
23:18:56.891 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 0 web.xml components
23:18:56.891 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building JSP to bytecode mappings...
23:18:56.891 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Skipping mapping creation - no JSP or bytecode components found
23:18:56.891 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building dependency graph...
23:18:56.891 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 0 components
23:18:56.891 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:18:56.892 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeProject in component: AnalysisService
23:18:56.892 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analysis completed successfully in 2 ms
23:18:56.896 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeProject in component: AnalysisService
23:18:56.896 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Starting comprehensive analysis of project: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit6234703826105312788
23:18:56.897 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing JSP files...
23:18:56.897 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJspFiles in component: JspAnalyzer
23:18:56.898 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 1 JSP files to analyze
23:18:56.898 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/1 ({:.1f}%) - 100.0
23:18:56.898 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit6234703826105312788/index.jsp
23:18:56.898 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit6234703826105312788/index.jsp
23:18:56.898 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit6234703826105312788/index.jsp
23:18:56.899 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit6234703826105312788/index.jsp
23:18:56.899 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
23:18:56.899 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 JSP components
23:18:56.899 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building JSP to bytecode mappings...
23:18:56.899 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Skipping mapping creation - no JSP or bytecode components found
23:18:56.899 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building dependency graph...
23:18:56.899 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 1 components
23:18:56.899 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:18:56.900 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeProject in component: AnalysisService
23:18:56.900 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analysis completed successfully in 3 ms
23:18:56.901 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeProject in component: AnalysisService
23:18:56.901 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Starting comprehensive analysis of project: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit6234703826105312788
23:18:56.901 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing Java source files...
23:18:56.901 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:18:56.902 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 1 Java files to analyze
23:18:56.903 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/1 ({:.1f}%) - 100.0
23:18:56.903 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit6234703826105312788/src/main/java/com/example/TestServlet.java
23:18:56.903 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit6234703826105312788/src/main/java/com/example/TestServlet.java
23:18:56.903 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit6234703826105312788/src/main/java/com/example/TestServlet.java
23:18:56.907 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit6234703826105312788/src/main/java/com/example/TestServlet.java
23:18:56.907 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:18:56.907 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 Java components
23:18:56.907 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building JSP to bytecode mappings...
23:18:56.907 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Skipping mapping creation - no JSP or bytecode components found
23:18:56.907 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building dependency graph...
23:18:56.907 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 1 components
23:18:56.907 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:18:56.909 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeProject in component: AnalysisService
23:18:56.909 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analysis completed successfully in 8 ms
23:18:56.921 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeProject in component: AnalysisService
23:18:56.921 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Starting comprehensive analysis of project: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10304633556137323344
23:18:56.921 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing JSP files...
23:18:56.921 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJspFiles in component: JspAnalyzer
23:18:56.922 [Test worker] INFO com.phodal.legacy.parser.JspAnalyzer -- Found 1 JSP files to analyze
23:18:56.922 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/1 ({:.1f}%) - 100.0
23:18:56.922 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing JSP file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10304633556137323344/index.jsp
23:18:56.922 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10304633556137323344/index.jsp
23:18:56.923 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10304633556137323344/index.jsp
23:18:56.923 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed JSP file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10304633556137323344/index.jsp
23:18:56.923 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJspFiles in component: JspAnalyzer
23:18:56.923 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 JSP components
23:18:56.923 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing Java source files...
23:18:56.923 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:18:56.924 [Test worker] INFO com.phodal.legacy.parser.JavaCodeAnalyzer -- Found 1 Java files to analyze
23:18:56.924 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/1 ({:.1f}%) - 100.0
23:18:56.924 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing Java file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10304633556137323344/src/main/java/com/example/TestServlet.java
23:18:56.924 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10304633556137323344/src/main/java/com/example/TestServlet.java
23:18:56.925 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10304633556137323344/src/main/java/com/example/TestServlet.java
23:18:56.928 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed Java file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10304633556137323344/src/main/java/com/example/TestServlet.java
23:18:56.928 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeJavaFiles in component: JavaCodeAnalyzer
23:18:56.928 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 Java components
23:18:56.928 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analyzing web.xml files...
23:18:56.928 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: analyzeWebXmlFiles in component: WebXmlParser
23:18:56.929 [Test worker] INFO com.phodal.legacy.parser.WebXmlParser -- Found 1 web.xml files to analyze
23:18:56.929 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4ca;<![CDATA[ Progress: 1/1 ({:.1f}%) - 100.0
23:18:56.929 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Analyzing web.xml file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10304633556137323344/WEB-INF/web.xml
23:18:56.932 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Analyzed web.xml file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10304633556137323344/WEB-INF/web.xml
23:18:56.932 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeWebXmlFiles in component: WebXmlParser
23:18:56.932 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Found 1 web.xml components
23:18:56.932 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building JSP to bytecode mappings...
23:18:56.932 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Skipping mapping creation - no JSP or bytecode components found
23:18:56.932 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Building dependency graph...
23:18:56.932 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Building dependency relationships for 3 components
23:18:56.932 [Test worker] INFO com.phodal.legacy.model.DependencyGraph -- Dependency graph built successfully
23:18:56.933 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: analyzeProject in component: AnalysisService
23:18:56.933 [Test worker] INFO com.phodal.legacy.services.AnalysisService -- Analysis completed successfully in 12 ms
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
