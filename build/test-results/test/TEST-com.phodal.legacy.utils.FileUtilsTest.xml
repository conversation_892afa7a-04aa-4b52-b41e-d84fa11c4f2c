<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.phodal.legacy.utils.FileUtilsTest" tests="15" skipped="0" failures="0" errors="0" timestamp="2025-07-09T15:18:56.941Z" hostname="fdhuang.local" time="0.071">
  <properties/>
  <testcase name="testIsJavaFile()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.003"/>
  <testcase name="testFindWebXmlFiles()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.006"/>
  <testcase name="testBackupFile()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.008"/>
  <testcase name="testIsJspFile()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.001"/>
  <testcase name="testEnsureDirectoryExists()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.003"/>
  <testcase name="testWriteFileContentCreatesDirectories()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.004"/>
  <testcase name="testFindJspFiles()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.007"/>
  <testcase name="testCountFilesByExtension()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.005"/>
  <testcase name="testReadAndWriteFileContent()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.004"/>
  <testcase name="testCopyFile()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.005"/>
  <testcase name="testGetFileNameWithoutExtension()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.003"/>
  <testcase name="testGetFileExtension()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.002"/>
  <testcase name="testFindJavaFiles()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.004"/>
  <testcase name="testGetRelativePath()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.002"/>
  <testcase name="testFindFilesByExtensions()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.004"/>
  <system-out><![CDATA[23:18:56.947 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: findWebXmlFiles in component: FileUtils
23:18:56.949 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Found 1 web.xml files in /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2857656606911764609/testdir
23:18:56.949 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: findWebXmlFiles in component: FileUtils
23:18:56.953 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Writing file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit178625661262283960/test.txt
23:18:56.954 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Written file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit178625661262283960/test.txt
23:18:56.957 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Created backup: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit178625661262283960/test.txt.backup.1752074336954
23:18:56.957 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit178625661262283960/test.txt.backup.1752074336954
23:18:56.957 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit178625661262283960/test.txt.backup.1752074336954
23:18:56.967 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Writing file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2352652862319759361/nested/deep/file.txt
23:18:56.968 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Written file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2352652862319759361/nested/deep/file.txt
23:18:56.968 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2352652862319759361/nested/deep/file.txt
23:18:56.968 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2352652862319759361/nested/deep/file.txt
23:18:56.973 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: findFilesByExtensions in component: FileUtils
23:18:56.975 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Found 3 files with extensions [.tag, .jsp, .jspx, .tagx] in /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11245428360749137101/testdir
23:18:56.975 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: findFilesByExtensions in component: FileUtils
23:18:56.985 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Writing file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit54590353115950263/test.txt
23:18:56.985 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Written file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit54590353115950263/test.txt
23:18:56.985 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit54590353115950263/test.txt
23:18:56.986 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit54590353115950263/test.txt
23:18:56.990 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Writing file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3414340471095770793/test.txt
23:18:56.991 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Written file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3414340471095770793/test.txt
23:18:56.991 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Copying to /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3414340471095770793/copied.txt file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3414340471095770793/test.txt
23:18:56.992 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Copied file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3414340471095770793/copied.txt
23:18:56.992 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3414340471095770793/copied.txt
23:18:56.992 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3414340471095770793/copied.txt
23:18:57.002 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: findFilesByExtensions in component: FileUtils
23:18:57.003 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Found 2 files with extensions [.java] in /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit6221690367433846416/testdir
23:18:57.003 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: findFilesByExtensions in component: FileUtils
23:18:57.009 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: findFilesByExtensions in component: FileUtils
23:18:57.010 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Found 2 files with extensions [.jsp] in /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit9925041906616779473/testdir
23:18:57.010 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: findFilesByExtensions in component: FileUtils
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
