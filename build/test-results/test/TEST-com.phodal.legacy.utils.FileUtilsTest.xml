<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.phodal.legacy.utils.FileUtilsTest" tests="15" skipped="0" failures="0" errors="0" timestamp="2025-07-09T15:06:05.302Z" hostname="fdhuang.local" time="0.055">
  <properties/>
  <testcase name="testIsJavaFile()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.002"/>
  <testcase name="testFindWebXmlFiles()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.006"/>
  <testcase name="testBackupFile()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.008"/>
  <testcase name="testIsJspFile()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.002"/>
  <testcase name="testEnsureDirectoryExists()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.004"/>
  <testcase name="testWriteFileContentCreatesDirectories()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.003"/>
  <testcase name="testFindJspFiles()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.004"/>
  <testcase name="testCountFilesByExtension()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.003"/>
  <testcase name="testReadAndWriteFileContent()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.002"/>
  <testcase name="testCopyFile()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.004"/>
  <testcase name="testGetFileNameWithoutExtension()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.002"/>
  <testcase name="testGetFileExtension()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.001"/>
  <testcase name="testFindJavaFiles()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.002"/>
  <testcase name="testGetRelativePath()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.001"/>
  <testcase name="testFindFilesByExtensions()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.003"/>
  <system-out><![CDATA[23:06:05.307 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: findWebXmlFiles in component: FileUtils
23:06:05.308 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Found 1 web.xml files in /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1599878049988787406/testdir
23:06:05.309 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: findWebXmlFiles in component: FileUtils
23:06:05.314 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Writing file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15768027668282719647/test.txt
23:06:05.315 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Written file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15768027668282719647/test.txt
23:06:05.318 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Created backup: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15768027668282719647/test.txt.backup.1752073565315
23:06:05.318 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15768027668282719647/test.txt.backup.1752073565315
23:06:05.318 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit15768027668282719647/test.txt.backup.1752073565315
23:06:05.329 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Writing file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit8741738388789200118/nested/deep/file.txt
23:06:05.329 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Written file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit8741738388789200118/nested/deep/file.txt
23:06:05.329 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit8741738388789200118/nested/deep/file.txt
23:06:05.330 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit8741738388789200118/nested/deep/file.txt
23:06:05.333 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: findFilesByExtensions in component: FileUtils
23:06:05.334 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Found 3 files with extensions [.tagx, .jspx, .jsp, .tag] in /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3396428859771849117/testdir
23:06:05.334 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: findFilesByExtensions in component: FileUtils
23:06:05.340 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Writing file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit279471464763406254/test.txt
23:06:05.340 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Written file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit279471464763406254/test.txt
23:06:05.340 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit279471464763406254/test.txt
23:06:05.340 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit279471464763406254/test.txt
23:06:05.343 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Writing file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit16357957431406353672/test.txt
23:06:05.343 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Written file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit16357957431406353672/test.txt
23:06:05.343 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Copying to /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit16357957431406353672/copied.txt file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit16357957431406353672/test.txt
23:06:05.344 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Copied file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit16357957431406353672/copied.txt
23:06:05.344 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit16357957431406353672/copied.txt
23:06:05.344 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit16357957431406353672/copied.txt
23:06:05.350 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: findFilesByExtensions in component: FileUtils
23:06:05.351 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Found 2 files with extensions [.java] in /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit3047458872559197624/testdir
23:06:05.351 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: findFilesByExtensions in component: FileUtils
23:06:05.356 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: findFilesByExtensions in component: FileUtils
23:06:05.356 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Found 2 files with extensions [.jsp] in /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10449992777866917187/testdir
23:06:05.356 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: findFilesByExtensions in component: FileUtils
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
