# Migrated Spring Boot Application

This application was automatically generated from a legacy JSP project using the JSP to Spring Boot migration tool.

## Getting Started

### Prerequisites
- Java 17 or higher
- Gradle 7.0 or higher

### Running the Application

1. Clone this repository
2. Navigate to the project directory
3. Run the application:
   ```bash
   ./gradlew bootRun
   ```

The application will start on `http://localhost:8080/api`

### API Endpoints

The application provides RESTful API endpoints under `/api`:

- `GET /api/posts` - Get all posts
- `GET /api/posts/{id}` - Get a specific post
- `POST /api/posts` - Create a new post
- `PUT /api/posts/{id}` - Update a post
- `DELETE /api/posts/{id}` - Delete a post

### Database

The application uses H2 in-memory database for development. You can access the H2 console at:
`http://localhost:8080/h2-console`

- JDBC URL: `jdbc:h2:mem:testdb`
- Username: `sa`
- Password: `password`

### Testing

Run tests with:
```bash
./gradlew test
```

### Building for Production

Build the application:
```bash
./gradlew build
```

The JAR file will be created in `build/libs/`

## Migration Notes

This application was migrated from a JSP-based application. Some manual adjustments may be needed:

1. Review and update business logic in service classes
2. Adjust database schema and entity relationships
3. Update API endpoints to match your requirements
4. Configure production database settings
5. Add authentication and authorization if needed

## Technology Stack

- Spring Boot 3.2.1
- Spring Data JPA
- H2 Database (development)
- Gradle
- Java 17
