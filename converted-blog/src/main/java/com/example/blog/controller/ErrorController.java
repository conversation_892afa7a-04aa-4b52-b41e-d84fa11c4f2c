// Generated REST controller from JSP pages: error.jsp
package com.example.blog.controller;

import java.lang.Long;
import java.lang.Void;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/errors")
@CrossOrigin
public class ErrorController {
  @DeleteMapping("/{id}")
  public ResponseEntity<Void> deleteError(@PathVariable Long id) {
    // TODO: Implement delete error logic;
    return ResponseEntity.noContent().build();
  }
}
