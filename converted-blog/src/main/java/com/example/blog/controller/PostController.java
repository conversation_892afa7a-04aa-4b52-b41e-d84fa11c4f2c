// Generated REST controller from JSP pages: edit.jsp, posts.jsp, post.jsp, create.jsp
package com.example.blog.controller;

import com.example.blog.entity.Post;
import java.lang.Long;
import java.lang.Void;
import java.util.Collections;
import java.util.List;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/posts")
@CrossOrigin
public class PostController {
  @PutMapping("/{id}")
  public ResponseEntity<Post> updatePost(@PathVariable Long id, @RequestBody Post post) {
    // TODO: Implement update post logic;
    return ResponseEntity.ok(post);
  }

  @GetMapping
  public ResponseEntity<List<Post>> getAllPosts() {
    // TODO: Implement get all posts logic;
    return ResponseEntity.ok(Collections.emptyList());
  }

  @GetMapping("/{id}")
  public ResponseEntity<Post> getPostById(@PathVariable Long id) {
    // TODO: Implement get post by id logic;
    return ResponseEntity.notFound().build();
  }

  @PostMapping
  public ResponseEntity<Post> createPost(@RequestBody Post post) {
    // TODO: Implement create post logic;
    return ResponseEntity.status(HttpStatus.CREATED).body(post);
  }

  @DeleteMapping("/{id}")
  public ResponseEntity<Void> deletePost(@PathVariable Long id) {
    // TODO: Implement delete post logic;
    return ResponseEntity.noContent().build();
  }
}
