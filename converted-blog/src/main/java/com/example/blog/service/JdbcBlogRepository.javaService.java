// Generated service class for entity: JdbcBlogRepository.java
package com.example.blog.service;

import com.example.blog.entity.JdbcBlogRepository.java;
import com.example.blog.repository.JdbcBlogRepository.javaRepository;
import java.lang.Long;
import java.util.List;
import java.util.Optional;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class JdbcBlogRepository.javaService {
  private final JdbcBlogRepository.javaRepository jdbcBlogRepository.javaRepository;

  public JdbcBlogRepository.javaService(
      JdbcBlogRepository.javaRepository jdbcBlogRepository.javaRepository) {
    this.jdbcBlogRepository.javaRepository = jdbcBlogRepository.javaRepository;
  }

  public List<JdbcBlogRepository.java> findAll() {
    return jdbcBlogRepository.javaRepository.findAll();
  }

  public Optional<JdbcBlogRepository.java> findById(Long id) {
    return jdbcBlogRepository.javaRepository.findById(id);
  }

  public JdbcBlogRepository.java save(JdbcBlogRepository.java jdbcblogrepository.java) {
    return jdbcBlogRepository.javaRepository.save(jdbcblogrepository.java);
  }

  public JdbcBlogRepository.java update(Long id, JdbcBlogRepository.java jdbcblogrepository.java) {
    jdbcblogrepository.java.setId(id);
    return jdbcBlogRepository.javaRepository.save(jdbcblogrepository.java);
  }

  public void deleteById(Long id) {
    jdbcBlogRepository.javaRepository.deleteById(id);
  }
}
