spring:
  application:
    name: migrated-app

  profiles:
    active: development

  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: password

  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true

  h2:
    console:
      enabled: true
      path: /h2-console

server:
  port: 8080
  servlet:
    context-path: /api

logging:
  level:
    com.example.blog: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

---
spring:
  config:
    activate:
      on-profile: production

  datasource:
    url: ****************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: ${DB_USERNAME:app_user}
    password: ${DB_PASSWORD:app_password}

  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false

logging:
  level:
    com.example.blog: INFO
    org.springframework.web: WARN
    org.hibernate.SQL: WARN
